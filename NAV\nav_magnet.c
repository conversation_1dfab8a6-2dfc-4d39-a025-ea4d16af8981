/***********************************************************************************
nav magnet module
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-5-9          | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/
#include "nav_includes.h"
#include "INS_Data.h"
#include "computerFrameParse.h"
extern AppSettingTypeDef hSetting;

void Save_mag_angle(_NAV_Data_Full_t* NAV_Data_Full_p)
{	
	//计算磁力计角度变化量
	double temp_angle = atan2(NAV_Data_Full_p->MAGNET.mag_use[1],NAV_Data_Full_p->MAGNET.mag_use[0]) - atan2(NAV_Data_Full_p->MAGNET.mag_use_pre[1],NAV_Data_Full_p->MAGNET.mag_use_pre[0]);
	if(temp_angle > 180 * DEG2RAD)
		temp_angle = temp_angle - 360*DEG2RAD;
	NAV_Data_Full_p->MAGNET.mag_angle = temp_angle * RAD2DEG;
	NAV_Data_Full_p->MAGNET.mag_use_buffer[NAV_Data_Full_p->MAGNET.magbuff_p] = temp_angle * RAD2DEG;
	NAV_Data_Full_p->MAGNET.magbuff_p++;
	NAV_Data_Full_p->MAGNET.magbuff_p = NAV_Data_Full_p->MAGNET.magbuff_p % 10;
	
	
}
void Get_Magnet_Data(_NAV_Data_Full_t* NAV_Data_Full_p,CombineDataTypeDef* CombineData_p)
{
	unsigned short i = 0;
	//Magnet数据
	int mag_index = 0;
	static int mag_count = 0;
	mag_index = NAV_Data_Full_p->magbuff_info.index;

	
		
	NAV_Data_Full_p->MAGNET.mag_raw[0] = CombineData_p->magInfo.magGrp[0];
	NAV_Data_Full_p->MAGNET.mag_raw[1] = CombineData_p->magInfo.magGrp[1];
	NAV_Data_Full_p->MAGNET.mag_raw[2] = CombineData_p->magInfo.magGrp[2];
	static char flag = 0;
#if 0
	if(norm(NAV_Data_Full_p->SINS.att_deg,3) > 40
		&&flag == 0)
	{
		NAV_Data_Full_p->MAGNET.mag_init2[0] = NAV_Data_Full_p->MAGNET.mag_raw[0];
		NAV_Data_Full_p->MAGNET.mag_init2[1] = NAV_Data_Full_p->MAGNET.mag_raw[1];
		NAV_Data_Full_p->MAGNET.mag_init2[2] = NAV_Data_Full_p->MAGNET.mag_raw[2];
		flag = 1;
	}
	//将磁力计偏心圆的圆心变换到（0，0，0）
	if(NAV_Data_Full.MAGNET.mag_fastflag == 1)
	{
		NAV_Data_Full_p->MAGNET.mag_cali[0] = NAV_Data_Full_p->MAGNET.mag_raw[0]-(NAV_Data_Full_p->MAGNET.x);
		NAV_Data_Full_p->MAGNET.mag_cali[1] = NAV_Data_Full_p->MAGNET.mag_raw[1]-(NAV_Data_Full_p->MAGNET.y);
		NAV_Data_Full_p->MAGNET.mag_cali[2] = NAV_Data_Full_p->MAGNET.mag_raw[2]-(NAV_Data_Full_p->MAGNET.z);
		NAV_Data_Full_p->MAGNET.mag_use[0] = NAV_Data_Full_p->MAGNET.mag_cali[0];
		NAV_Data_Full_p->MAGNET.mag_use[1] = NAV_Data_Full_p->MAGNET.mag_cali[1];
		NAV_Data_Full_p->MAGNET.mag_use[2] = NAV_Data_Full_p->MAGNET.mag_cali[2];
	}
#endif
	//if(NAV_Data_Full.mag_cali_flag == 0)
	{
		NAV_Data_Full_p->MAGNET.mag_cali[0] = NAV_Data_Full_p->MAGNET.mag_raw[0]-(NAV_Data_Full_p->MAGNET_BUFF.Magoff[0]);
		NAV_Data_Full_p->MAGNET.mag_cali[1] = NAV_Data_Full_p->MAGNET.mag_raw[1]-(NAV_Data_Full_p->MAGNET_BUFF.Magoff[1]);
		NAV_Data_Full_p->MAGNET.mag_cali[2] = NAV_Data_Full_p->MAGNET.mag_raw[2]-(NAV_Data_Full_p->MAGNET_BUFF.Magoff[2]);
		if(NAV_Data_Full_p->MAGNET_BUFF.Magoff[3] != 0
			&&NAV_Data_Full_p->MAGNET_BUFF.Magoff[4] !=0
			&&NAV_Data_Full_p->MAGNET_BUFF.Magoff[5] !=0)
		{
			NAV_Data_Full_p->MAGNET.mag_use_pre[0] = NAV_Data_Full_p->MAGNET.mag_use[0];
			NAV_Data_Full_p->MAGNET.mag_use_pre[1] = NAV_Data_Full_p->MAGNET.mag_use[1];
			NAV_Data_Full_p->MAGNET.mag_use_pre[2] = NAV_Data_Full_p->MAGNET.mag_use[2];
			NAV_Data_Full_p->MAGNET.mag_use[0] = NAV_Data_Full_p->MAGNET.mag_cali[0]/NAV_Data_Full_p->MAGNET_BUFF.Magoff[3];
			NAV_Data_Full_p->MAGNET.mag_use[1] = NAV_Data_Full_p->MAGNET.mag_cali[1]/NAV_Data_Full_p->MAGNET_BUFF.Magoff[4];
			NAV_Data_Full_p->MAGNET.mag_use[2] = NAV_Data_Full_p->MAGNET.mag_cali[2]/NAV_Data_Full_p->MAGNET_BUFF.Magoff[5];
		}
		Save_mag_angle(&NAV_Data_Full);	
			
	}
	if (NAV_Data_Full_p->magbuff_info.magbuff_flag== 0)
	{
		if (mag_index ==MAG_BUFFER_SIZE)
		{
			NAV_Data_Full_p->magbuff_info.magbuff_flag= 1;
		}
		mag_count = mag_count % (SAMPLE_FREQ/SAMPLE_FREQ_MAG);
		if(mag_count == 0)
		{	
		
		mag_index = mag_index % MAG_BUFFER_SIZE;
		//判断当前磁力计数据与缓冲区中对比，方向不同的磁力计放入缓冲区
			if (NAV_Data_Full_p->magbuff_info.magbuff_flag == 0 )
			//磁力计数据存缓冲区
			{
				double need_save_index = 1;//1为需要存储，0为不需要存储
				
				if (mag_index == 0 && NAV_Data_Full_p->MAGNET.mag_raw[0] != 0 && norm(NAV_Data_Full_p->MAGNET.mag_raw, 3)<6500)
				{
					NAV_Data_Full_p->MAGNET_BUFF.MAGNET_buffer[0] = NAV_Data_Full_p->MAGNET.mag_raw[0];
					NAV_Data_Full_p->MAGNET_BUFF.MAGNET_buffer[1] = NAV_Data_Full_p->MAGNET.mag_raw[1];
					NAV_Data_Full_p->MAGNET_BUFF.MAGNET_buffer[2] = NAV_Data_Full_p->MAGNET.mag_raw[2];
					//校准磁力计数据参数，//X=inv(K'*K)*K'*Y
					NAV_Data_Full_p->MAGNET_BUFF.MagK[0] = NAV_Data_Full_p->MAGNET_BUFF.MAGNET_buffer[1] * NAV_Data_Full_p->MAGNET_BUFF.MAGNET_buffer[1];//y^2
					NAV_Data_Full_p->MAGNET_BUFF.MagK[1*MAG_BUFFER_SIZE] = NAV_Data_Full_p->MAGNET_BUFF.MAGNET_buffer[2] * NAV_Data_Full_p->MAGNET_BUFF.MAGNET_buffer[2];//z^2
					NAV_Data_Full_p->MAGNET_BUFF.MagK[2*MAG_BUFFER_SIZE]=NAV_Data_Full_p->MAGNET_BUFF.MAGNET_buffer[0];//x
					NAV_Data_Full_p->MAGNET_BUFF.MagK[3*MAG_BUFFER_SIZE]=NAV_Data_Full_p->MAGNET_BUFF.MAGNET_buffer[1];//y
					NAV_Data_Full_p->MAGNET_BUFF.MagK[4*MAG_BUFFER_SIZE]=NAV_Data_Full_p->MAGNET_BUFF.MAGNET_buffer[2];//z
					NAV_Data_Full_p->MAGNET_BUFF.MagK[5*MAG_BUFFER_SIZE]=1.0;//1
					NAV_Data_Full_p->MAGNET_BUFF.MagY[0] = -NAV_Data_Full_p->MAGNET_BUFF.MAGNET_buffer[0] * NAV_Data_Full_p->MAGNET_BUFF.MAGNET_buffer[0];//-x^2
					mag_index++;
				}
				else 
				{
					for (int i=0;i<mag_index;i++)//判断当前磁力计读书与缓冲区中是否有相似的点，如果无则存储。
					{	
						if( fabs(NAV_Data_Full_p->MAGNET_BUFF.MAGNET_buffer[i * 3 + 0] - NAV_Data_Full_p->MAGNET.mag_raw[0]) < 300
							&&fabs(NAV_Data_Full_p->MAGNET_BUFF.MAGNET_buffer[i * 3 + 1] - NAV_Data_Full_p->MAGNET.mag_raw[1]) < 300
							&&fabs(NAV_Data_Full_p->MAGNET_BUFF.MAGNET_buffer[i * 3 + 2] - NAV_Data_Full_p->MAGNET.mag_raw[2]) < 300
							)
						{
							need_save_index = 0;
						}
					}
					if (need_save_index == 1 
						&& norm(NAV_Data_Full_p->MAGNET.mag_raw, 3) < 10000
						&& norm(NAV_Data_Full_p->MAGNET.mag_raw, 3) > 0)
					{
						NAV_Data_Full_p->MAGNET_BUFF.MAGNET_buffer[mag_index*3+0] = NAV_Data_Full_p->MAGNET.mag_raw[0];
						NAV_Data_Full_p->MAGNET_BUFF.MAGNET_buffer[mag_index*3+1] = NAV_Data_Full_p->MAGNET.mag_raw[1];
						NAV_Data_Full_p->MAGNET_BUFF.MAGNET_buffer[mag_index*3+2] = NAV_Data_Full_p->MAGNET.mag_raw[2];
						//校准磁力计数据参数，//X=inv(K'*K)*K'*Y
						NAV_Data_Full_p->MAGNET_BUFF.MagK[mag_index] = NAV_Data_Full_p->MAGNET_BUFF.MAGNET_buffer[mag_index*3+1] * NAV_Data_Full_p->MAGNET_BUFF.MAGNET_buffer[mag_index*3+1];//y^2
						NAV_Data_Full_p->MAGNET_BUFF.MagK[mag_index+1*MAG_BUFFER_SIZE] = NAV_Data_Full_p->MAGNET_BUFF.MAGNET_buffer[mag_index*3+2] * NAV_Data_Full_p->MAGNET_BUFF.MAGNET_buffer[mag_index*3+2];//z^2
						NAV_Data_Full_p->MAGNET_BUFF.MagK[mag_index+2*MAG_BUFFER_SIZE]=NAV_Data_Full_p->MAGNET_BUFF.MAGNET_buffer[mag_index*3+0];//x
						NAV_Data_Full_p->MAGNET_BUFF.MagK[mag_index+3*MAG_BUFFER_SIZE]=NAV_Data_Full_p->MAGNET_BUFF.MAGNET_buffer[mag_index*3+1];//y
						NAV_Data_Full_p->MAGNET_BUFF.MagK[mag_index+4*MAG_BUFFER_SIZE]=NAV_Data_Full_p->MAGNET_BUFF.MAGNET_buffer[mag_index*3+2];//z
						NAV_Data_Full_p->MAGNET_BUFF.MagK[mag_index+5*MAG_BUFFER_SIZE]=1.0;//1
						NAV_Data_Full_p->MAGNET_BUFF.MagY[mag_index] = -NAV_Data_Full_p->MAGNET_BUFF.MAGNET_buffer[mag_index*3+0] * NAV_Data_Full_p->MAGNET_BUFF.MAGNET_buffer[mag_index*3+0];//-x^2
						mag_index++;
						need_save_index = 0;
					}
				}
			}
		}
		mag_count++;
		NAV_Data_Full_p->magbuff_info.index = mag_index;
	}
}
	
//初始化计算航向
//参考软件设计说明书18.1	AHRS互补滤波(Mahony)算法
double MagInitHeading(double *Mag, double *att,double *pos)
{
	double tmp1,tmp2;
	double temp;
#if 0
	temp = Mag[0];
	Mag[0] = -Mag[1];
	Mag[1] = temp;
#endif
	//att[0]横滚，att[1]俯仰
	tmp1	=	Mag[0]*cos(att[1])+Mag[2]*sin(att[1]);
	tmp2	=	Mag[1]*cos(att[0])+Mag[0]*sin(att[0])*sin(att[1])-Mag[2]*sin(att[0])*cos(att[1]);
	att[2]	=	atan2f(tmp1,tmp2) + (NAV_Data_Full.MAGNET.mag_dec*DEG2RAD);//arctan(emp1/tmp2)
	return att[2] ;//磁偏角
}




//磁力计标定
void MagInitCali(_NAV_Data_Full_t* NAV_Data_Full_p)
{
	double temp_magK[6 * 6] = {0};
	double temp_mag[MAG_BUFFER_SIZE * 6]={0};
	double norm_mag[3] = {0};
	static int invstaus = 1;
	//X=inv(K'*K)*K'*Y
	//从mag缓冲区读取数据
	//matmul("NN", 3, 1, 3, 1.0, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->MAGNET.mag_use, 0.0, tmp_mag);
	//NAV_Data_Full_p->MAGNET_BUFF.MagK
	//按行存储，按列运算
	matmul("TN",6, 6, MAG_BUFFER_SIZE, 1.0,NAV_Data_Full_p->MAGNET_BUFF.MagK,NAV_Data_Full_p->MAGNET_BUFF.MagK,0.0,temp_magK);
	//6*6矩阵求逆
	invstaus = matinv(temp_magK, 6);
	// 6*6 * 6*n = 6*n
	if (invstaus==0 )
	{
	matmul("NT",6, MAG_BUFFER_SIZE, 6, 1.0,temp_magK,NAV_Data_Full_p->MAGNET_BUFF.MagK,0.0,temp_mag);
	//6*n * n*1 =6*1;
	matmul("NN",6, 1, MAG_BUFFER_SIZE, 1.0,temp_mag,NAV_Data_Full_p->MAGNET_BUFF.MagY,0.0,NAV_Data_Full_p->MAGNET_BUFF.MagX);
	//根据最小二乘求解的X，解出椭圆的圆心和三轴。
	norm_mag[0] = sqrt(NAV_Data_Full_p->MAGNET_BUFF.Magoff[0]*NAV_Data_Full_p->MAGNET_BUFF.Magoff[0]+NAV_Data_Full_p->MAGNET_BUFF.MagX[0]*NAV_Data_Full_p->MAGNET_BUFF.Magoff[1]*NAV_Data_Full_p->MAGNET_BUFF.Magoff[1]+NAV_Data_Full_p->MAGNET_BUFF.MagX[1]*NAV_Data_Full_p->MAGNET_BUFF.Magoff[2]*NAV_Data_Full_p->MAGNET_BUFF.Magoff[2]-NAV_Data_Full_p->MAGNET_BUFF.MagX[5]);
	//norm_mag[1] = sqrt(NAV_Data_Full_p->MAGNET_BUFF.Magoff[3]*NAV_Data_Full_p->MAGNET_BUFF.Magoff[3]/NAV_Data_Full_p->MAGNET_BUFF.MagX[0]);
	//norm_mag[2] = sqrt(NAV_Data_Full_p->MAGNET_BUFF.Magoff[3]*NAV_Data_Full_p->MAGNET_BUFF.Magoff[3]/NAV_Data_Full_p->MAGNET_BUFF.MagX[1]);
	//if(fabs(norm_mag[0]-5200) <300
		//&&fabs(norm_mag[1]-5200) <300
		//&&fabs(norm_mag[2]-5200) <300)
	{
	NAV_Data_Full_p->MAGNET_BUFF.Magoff[0] = -NAV_Data_Full_p->MAGNET_BUFF.MagX[2]/2;//x
	NAV_Data_Full_p->MAGNET_BUFF.Magoff[1] = -NAV_Data_Full_p->MAGNET_BUFF.MagX[3]/(2*NAV_Data_Full_p->MAGNET_BUFF.MagX[0]);//y
	NAV_Data_Full_p->MAGNET_BUFF.Magoff[2] = -NAV_Data_Full_p->MAGNET_BUFF.MagX[4]/(2*NAV_Data_Full_p->MAGNET_BUFF.MagX[1]);//z
	NAV_Data_Full_p->MAGNET_BUFF.Magoff[3] = norm_mag[0];
	NAV_Data_Full_p->MAGNET_BUFF.Magoff[4] = sqrt(NAV_Data_Full_p->MAGNET_BUFF.Magoff[3]*NAV_Data_Full_p->MAGNET_BUFF.Magoff[3]/NAV_Data_Full_p->MAGNET_BUFF.MagX[0]);
	NAV_Data_Full_p->MAGNET_BUFF.Magoff[5] = sqrt(NAV_Data_Full_p->MAGNET_BUFF.Magoff[3]*NAV_Data_Full_p->MAGNET_BUFF.Magoff[3]/NAV_Data_Full_p->MAGNET_BUFF.MagX[1]);
	//标定结束之后，将标定位设置为0
	NAV_Data_Full_p->mag_cali_flag = 0 ;
	MagInitHeading(NAV_Data_Full_p->MAGNET.mag_use,NAV_Data_Full_p->SINS.att,NAV_Data_Full_p->SINS.pos);
	}
	//else 
	{
	//NAV_Data_Full_p->magbuff_info.index = 0;//重新开始缓存磁力计数据
	//NAV_Data_Full_p->magbuff_info.magbuff_flag= 0;
	}
	//memcpy((void*)&hSetting.circle_x,&NAV_Data_Full_p->MAGNET_BUFF.Magoff[0], sizeof(NAV_Data_Full_p->MAGNET_BUFF.Magoff[0]));
	//memcpy((void*)&hSetting.circle_y,&NAV_Data_Full_p->MAGNET_BUFF.Magoff[1], sizeof(NAV_Data_Full_p->MAGNET_BUFF.Magoff[1]));
	//memcpy((void*)&hSetting.circle_z,&NAV_Data_Full_p->MAGNET_BUFF.Magoff[2], sizeof(NAV_Data_Full_p->MAGNET_BUFF.Magoff[2]));
	//if(fabs(NAV_Data_Full_p->MAGNET_BUFF.Magoff[3]-5200) <300
		//&&fabs(NAV_Data_Full_p->MAGNET_BUFF.Magoff[4]-5200) <300
		//&&fabs(NAV_Data_Full_p->MAGNET_BUFF.Magoff[5]-5200) <300)
	{
	hSetting.circle_x = NAV_Data_Full_p->MAGNET_BUFF.Magoff[0];
	hSetting.circle_y = NAV_Data_Full_p->MAGNET_BUFF.Magoff[1];
	hSetting.circle_z = NAV_Data_Full_p->MAGNET_BUFF.Magoff[2];
	hSetting.circle_a = NAV_Data_Full_p->MAGNET_BUFF.Magoff[3];
	hSetting.circle_b = NAV_Data_Full_p->MAGNET_BUFF.Magoff[4];
	hSetting.circle_c = NAV_Data_Full_p->MAGNET_BUFF.Magoff[5];
	comm_set_customPara();
	}
	if(NAV_Data_Full_p->MAGNET_BUFF.Magoff[3] == 0
		&&NAV_Data_Full_p->MAGNET_BUFF.Magoff[4] == 0
		&&NAV_Data_Full_p->MAGNET_BUFF.Magoff[5] == 0)
	{
	hSetting.circle_a = 5200;
	hSetting.circle_b = 5200;
	hSetting.circle_c = 5200;
	comm_set_customPara();
	}
	}
}


//处理磁场数据
unsigned char ProMagData()
{
	return 0;
}

//磁北转真北，如果需要可以WMM，参考代码WMM2020_Linux
double  CalMagDeclination(double heading, double *pos)
{
	return heading;
}

void magFastCali()
{	
	//根据磁航向和陀螺航向计算磁力计零偏
	double temp4 = 0;
	double temp5 = 0;
	double temp6 = 0;
	static double circle_O[3]  = {0};
	double temp1[3] = {0};
	double temp2[3] = {0};
	double temp3[3] = {0};
	
#if 0
	//if (NAV_Data_Full.SINS.att[2] > 30*RAD2DEG)
	//计算磁力计内积
	
	temp4 = (NAV_Data_Full.MAGNET.mag_init[0] - circle_O[0] ) * (NAV_Data_Full.MAGNET.mag_use[0] - circle_O[0]) + (NAV_Data_Full.MAGNET.mag_init[1] - circle_O[0]) * (NAV_Data_Full.MAGNET.mag_use[1]-circle_O[0]);
	temp5 = sqrt(NAV_Data_Full.MAGNET.mag_init[0] * NAV_Data_Full.MAGNET.mag_init[0] + NAV_Data_Full.MAGNET.mag_init[1]*NAV_Data_Full.MAGNET.mag_init[1])
	* sqrt(NAV_Data_Full.MAGNET.mag_use[0] * NAV_Data_Full.MAGNET.mag_use[0] + NAV_Data_Full.MAGNET.mag_use[1] * NAV_Data_Full.MAGNET.mag_use[1]);
	temp6 = temp4/temp5;
	//圆心位置
	temp4 = acos(temp6) - fabs(NAV_Data_Full.SINS.att[2]);
	 //circle_O[0] -= temp1 * NAV_Data_Full.MAGNET.mag_init[0];
	// circle_O[1] -= temp1 * NAV_Data_Full.MAGNET.mag_init[1];
	
	if (temp4 <=0.1*DEG2RAD)//当磁力计误差和陀螺航向误差小于0.1时，结束校准
	{
		//NAV_Data_Full.MAGNET.x = - circle_O[0];
		//NAV_Data_Full.MAGNET.y = - circle_O[1];
		//NAV_Data_Full.MAGNET.mag_fastflag = 1 ; //校正完成设置1
	}
#endif
	double s = 0;
	//去除当前圆心坐标
	for (int i =0;i<3;i++)
	{
		temp1[i] = NAV_Data_Full.MAGNET.mag_init[i] - circle_O[i];
		//temp2[i] = NAV_Data_Full.MAGNET.mag_init2[i] - circle_O[i];
		temp3[i] = NAV_Data_Full.MAGNET.mag_raw[i] -circle_O[i];
	}
	static char xycali = 0;// xy轴圆心标定标志位
	if(xycali == 0)
	{
		s =norm(temp1,3)/norm(temp1,2);
		temp1[0] = temp1[0] * s;
		temp1[1] = temp1[1] * s;
		s =norm(temp3,3)/norm(temp3,2);
		temp3[0] = temp3[0] * s;
		temp3[1] = temp3[1] * s;
		if(s < 2)
		for (int i =0;i<2;i++)
		{
			circle_O[i] -= (5200.0 - norm(temp1, 2))/5200.0 * temp1[i] ;
			//circle_O[i] -= (5200.0 - norm(temp2, 2))/5200.0 * temp2[i];
			circle_O[i] -= (5200.0 - norm(temp3, 2))/5200.0 * temp3[i];
		}
	}
	//根据当前磁力计大小移动圆心
	
	temp4 = (temp1[0]  ) * (temp3[0]) + (temp1[1] ) * (temp3[1]);
	temp5 = sqrt(temp1[0] * temp1[0] + temp1[1]*temp1[1]) * sqrt(temp3[0] * temp3[0] + temp3[1]*temp3[1]);
	temp6 = temp4/temp5;
	//圆心位置
	temp4 = acos(temp6) - fabs(NAV_Data_Full.SINS.att[2]);
	if (xycali ==2)
	{
		circle_O[2] -= (5200.0 - norm(temp1, 3)) * temp1[2];
		circle_O[2] -= (5200.0 - norm(temp3, 3)) * temp3[2];
		if (norm(temp1,3)-5200 < 50
		&&norm(temp3,3)-5200 < 50)
		{
		NAV_Data_Full.MAGNET.mag_fastflag = 1 ;
		NAV_Data_Full.MAGNET.z = circle_O[2];
		}
	}
	if (norm(temp1,2)-5200 < 100
		&&norm(temp3,2)-5200 < 100
		&&fabs(temp4) < 0.5*DEG2RAD
		&&xycali == 0
		)
	{	
		//xycali = 1;
		//NAV_Data_Full.MAGNET.mag_fastflag = 1 ; //校正完成设置1
		NAV_Data_Full.MAGNET.x = circle_O[0];
		NAV_Data_Full.MAGNET.y = circle_O[1];
		//NAV_Data_Full.MAGNET.z = circle_O[2];
	}
	
	
		
	
}
//判断是否需要标定
void ismagclai()
{	
	double temp1[3] ={0};
	double normnum = 0;
	double tmp_norm =0;
	for(int i =0;i < MAG_BUFFER_SIZE;i++)
	{
		temp1[0] = NAV_Data_Full.MAGNET_BUFF.MAGNET_buffer[i*3 + 0] -  NAV_Data_Full.MAGNET_BUFF.Magoff[0] ;
		temp1[1] = NAV_Data_Full.MAGNET_BUFF.MAGNET_buffer[i*3 + 1] -  NAV_Data_Full.MAGNET_BUFF.Magoff[1] ;
		temp1[2] = NAV_Data_Full.MAGNET_BUFF.MAGNET_buffer[i*3 + 2] -  NAV_Data_Full.MAGNET_BUFF.Magoff[2] ;
		tmp_norm = fabs(norm(temp1,3) - (NAV_Data_Full.MAGNET_BUFF.Magoff[3] + NAV_Data_Full.MAGNET_BUFF.Magoff[4] + NAV_Data_Full.MAGNET_BUFF.Magoff[5])/3);//计算缓冲区去除当前圆心之后的模值，然后和标定数值比较
		normnum += tmp_norm;
		if (tmp_norm > 1000)
		{
			
		}
	}
	if (normnum >200 * MAG_BUFFER_SIZE
		)//如果磁力计模值差大于200，则认为之前的校准数据不可用，需要重新校准
	{
		NAV_Data_Full.mag_cali_flag = 1 ;
	}
	
}
//初始化磁场数据

void MagInit(_NAV_Data_Full_t* NAV_Data_Full_p)
{	
	NAV_Data_Full_p->magbuff_info.magbuff_flag = 0;
	NAV_Data_Full_p->MAGNET.mag_dec = -3.36667;//磁偏角 
	//NAV_Data_Full_p->mag_cali_flag = 1 ;
	//NAV_Data_Full.MAGNET.mag_fastflag == 0;
	NAV_Data_Full_p->MAGNET_BUFF.Magoff[0] = hSetting.circle_x;
	NAV_Data_Full_p->MAGNET_BUFF.Magoff[1] = hSetting.circle_y;
	NAV_Data_Full_p->MAGNET_BUFF.Magoff[2] = hSetting.circle_z;
	NAV_Data_Full_p->MAGNET_BUFF.Magoff[3] = hSetting.circle_a;//磁模长
	NAV_Data_Full_p->MAGNET_BUFF.Magoff[4] = hSetting.circle_b;
	NAV_Data_Full_p->MAGNET_BUFF.Magoff[5] = hSetting.circle_c;
	//初始化磁大小为5200
	if(NAV_Data_Full_p->MAGNET_BUFF.Magoff[3] <= 3000
		&&NAV_Data_Full_p->MAGNET_BUFF.Magoff[4] <= 3000
		&&NAV_Data_Full_p->MAGNET_BUFF.Magoff[5] <= 3000)
	{
	hSetting.circle_a = 5200;
	hSetting.circle_b = 5200;
	hSetting.circle_c = 5200;
	comm_set_customPara();
	}
	//判断模长范围，过大或者过小都认为错误
	if(NAV_Data_Full_p->MAGNET_BUFF.Magoff[3] >= 7000
		&&NAV_Data_Full_p->MAGNET_BUFF.Magoff[4] >= 7000
		&&NAV_Data_Full_p->MAGNET_BUFF.Magoff[5] >= 7000)
	{
	hSetting.circle_a = 5200;
	hSetting.circle_b = 5200;
	hSetting.circle_c = 5200;
	comm_set_customPara();
	}
	
}



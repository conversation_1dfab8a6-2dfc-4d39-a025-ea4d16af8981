/***********************************************************************************
This file include all header file and enum parameters 
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-2-4          | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/
#ifndef __NAV_INCLUDEES_H__
#define __NAV_INCLUDEES_H__

//#include "NAV_MCU.h"
#include "math.h"
//#include "arm_math.h"

#include "nav_type.h"

#include "nav_math.h"
#include "nav_const.h"
#include "nav_sins.h"
#include "nav_ods.h"
#include "nav_kf.h"
#include "nav_app.h" 
#include "nav.h"
#include "navlog.h"
#include "algorithm.h"
#include "nav_magnet.h"
#include "nav_mahony.h"
#include "nav_cli.h"
#include "nav_imu.h"
#include "lpbus.h"






/**********************************************************************************
V:�汾
X1���㷨
001���ͻ�
B:��չ�汾
01:����汾�ţ������ۼ�
R:���а汾��
01������汾�ţ������ۼ�
D001�����ڵ��Թ�����ʱ����Ҫ�ٰ汾�ź���Ӻ�׺D001�����汾ʱ�����ۼ�
************************************************************************************/
#define SINS_ALGORITHM_BUILD_VERSION  "VX1.001.B01R01.D001"



#define  RETURN_SUCESS			1
#define  RETURN_FAIL 			0

//���ٶ����̶�/s
#define MAX_GYRO_VALUE         1000
//���ٶ�����m/s2
#define MAX_ACC_VALUE          (10*G0)
//���˱۵�λm
#define MAX_ARM_VALUE          20
//��ֹʱ������ٶ��������m/s2
#define MAX_STATIC_VN          0.05       


//�ж�ת����С ��/s
#define MIN_TURN_GYRO_VALUE    1.0f 

#define ALLIGN_DIFF_ACC_USE   1.5//��ʼУ׼ʱ��Ӽ�ʱ�䵥�Χm/s2

#ifndef NULL
#define NULL 0
#endif
//��̬У��������ֵ�����ھ�̬ƽ�����ٶȡ����ٶȹ���
#define MAX_STATIC_STORED_COUNT 400

//IMU����ֵ�������
#define GYRO_ABNORMAL_THRESHOLD  100//unit:degree/s
#define ACC_ABNORMAL_THRESHOLD   20//unit:m/s2


//Ӧ�ó�������
enum EUSECASES 
{
    E_USE_CASE_In_Vehicle =0,       			//����ģʽ
    E_USE_CASE_In_UAV=1,						//���˻�ģʽ
};



//�㷨����
enum EMETHODTYPE 
{
    E_METHOD_TYPE_KALMAN =0,       				//����kalman�˲�
    E_METHOD_TYPE_DR=1,							//ֱ���ٶ�����
};

//�Ƿ�������ģʽ
enum ESIMMODE
{
    E_SIM_MODE_NORMAL =0,       				//����ģʽΪ��������
    E_SIM_MODE_DEBUG=1,		   					//����ģʽΪ����ģʽ
};


enum EIMUSELECT 
{
    E_IMU_ATT_MEMS =0,       					//mems
    E_IMU_ATT_IFOG=1,							//ifog
};

enum EIMUMANUNAME
{
    E_IMU_MANU_460 =0,       					//imu460
    E_IMU_MANU_SCHA63X=1,						//scha63x
    E_IMU_MANU_ADIS16465=2,						//adis16465
    E_IMU_MANU_EPSON_G370=3,					//epson7370
};



enum ELONLATHEMISPHERE
{
    E_LON_EAST_Hemisphere 	='E', 				//����     	
    E_LON_WEST_Hemisphere 	='W',				//����			
    E_LAT_NORTH_Hemisphere 	='N',				//��γ       	
    E_LAT_SOUTH_Hemisphere 	='S',				//��γ
};


//gps��λ״̬
enum EGPSPOSSTATUS
{
    E_GPS_POS_VALID =0x41,       				//��Ч'A'
    E_GPS_POS_INVALID=0x56,		 				// ��Ч'V'
};

//��ϵ����㷨�У�֧�ֵ�GNSS״̬
enum ENAVSUPPORTGNSSSATUS
{
    E_NAV_SUPPORT_RTK_FIEX_STATUS=0,			//����֧�ֶ�rtk�̶����Լ��  
	E_NAV_SUPPORT_RTK_ALL_STATUS=1, 			//֧��SPP��DGPS��FIEXED��FLOAT���Լ��
};

//rtk״̬
enum EGPSRTKSTATUS
{
    E_GPS_RTK_INVALID=0, 						//0: �޶�λ
	E_GPS_RTK_SPP=1,							//1�����㶨λ  		
    E_GPS_RTK_DGPS=2,							//2����ֽ�
    E_GPS_RTK_FIXED=4,							//4��RTK�̶���
    E_GPS_RTK_FLOAT=5,							//5��RTK������
    E_GPS_RTK_TOTAL=6,
    
};

//������λ״̬
//2��ǰ��4�����
enum EODSGEARSTATUS
{
	E_ODS_GEAR_STATIC=0,  
    E_ODS_GEAR_FORWARD=2,  
	E_ODS_GEAR_BACKWARD=4, 
};

enum ENAVSTATUS
{
    E_NAV_STATUS_START =0,       	        //��ʼ�������ص�������
    E_NAV_STATUS_ROUTH_ALIGN=1,		    	//SINS����׼
    E_NAV_STATUS_SINS_KF_INITIAL=2,      	//SINS��KF��ʼ��
    E_NAV_STATUS_SYSTEM_STANDARD=3,      	//ϵͳ�궨
    E_NAV_STATUS_IN_NAV=4,                 	//��������
    E_NAV_STATUS_STOP=5,                   	//ֹͣ����
    E_NAV_STATUS_TOTAL=6,					//�ܹ�״̬��Ŀ
};

//GPS���ݸ���״̬
enum EGPSUPDATESTATUS
{
	E_GPS_NO_UPDATE=0,						//GPS����δ����
	E_GPS_IS_UPDATE=1,						//GPS���ݸ���
};

//�ں϶�λ״̬
enum EFUSHIONSTATUS
{
	E_FUNSION_NONE=0,						//��Ч�ں�
	E_FUNSION_GPS=1,						//GPS�ں�
	E_FUNSION_WHEEL=2,						//��wheel�ں�
	E_FUNSION_MOTION=3,						//�˶�ģ��Լ��
	E_FUNSION_HEAVE=4,						//升沉算法约束(水面应用)
	E_FUNSION_TOTAL=5,
};

//�Ƿ���ODS����
enum EODSWHEELFLAGSTATUS
{
	E_ODS_WHEEL_FLAG_NONE=0,				//û��ODS����
	E_ODS_WHEEL_FLAG_HAVE=1,				//��ODS����
};

//������±�־
enum EKALMANMEASUREUPDATE
{
	E_KALMAN_MEASURE_UPDATE_NONE=0,			//�������������
	E_KALMAN_MEASURE_UPDATE_HAVE=1,			//�����������
};

//��������Ƿ��к���Լ��
enum EKALMANMEASUREHEADING
{
	E_KALMAN_MEASURE_HEADING_NO=0,			//�����к����������
	E_KALMAN_MEASURE_HEADING_YES=1,			//���к����������
};

//��������Ƿ����ٶ�Լ��
enum EKALMANMEASUREVEL
{
	E_KALMAN_MEASURE_VEL_NO=0,				//�������ٶ��������
	E_KALMAN_MEASURE_VEL_YES=1,				//�����ٶ��������
};

//��������Ƿ���λ��Լ��
enum EKALMANMEASUREPOS
{
	E_KALMAN_MEASURE_POS_NO=0,				//������λ���������
	E_KALMAN_MEASURE_POS_YES=1,				//����λ���������
};




//�������˲����²���
#define KLMAN_FILTER_SETP_SUM        6		//�������˲���������
enum EKALMANFILTERSTEP
{
	E_KALMAN_FILTER_NONE=0,					//��δ��ʼkalman�˲�
	E_KALMAN_TIME_UPDATE_1=1,				//�������˲�ʱ�����1
	E_KALMAN_TIME_UPDATE_2=2,				//�������˲�ʱ�����2
	E_KALMAN_TIME_UPDATE_3=3,				//�������˲�ʱ�����3
	E_KALMAN_TIME_UPDATE_4=4,				//�������˲�ʱ�����4
	E_KALMAN_MEASURE_UPDATE_1=5,			//�������˲��������1
	E_KALMAN_MEASURE_UPDATE_2=6,			//�������˲��������2
};

enum ENAVSTANDARDFLAGSTATUS
{
	E_NAV_STANDARD_NO_PROCCSS=0,			//0:δ�궨��� 
	E_NAV_STANDARD_PROCCSSING=1,			//1���궨��
	E_NAV_STANDARD_PROCCSSED=2,				//2���궨���
	E_NAV_STANDARD_TOTAL=3,					//�ܹ�״̬��Ŀ
};


//��Ϣͳ����
static char* g_NavStatusStaTxt[E_NAV_STATUS_TOTAL] =
{
    "E_NAV_STATUS_START" ,      	        //��ʼ�������ص�������
    "E_NAV_STATUS_ROUTH_ALIGN",		    	//SINS����׼
    "E_NAV_STATUS_SINS_KF_INITIAL",      	//SINS��KF��ʼ��
    "E_NAV_STATUS_SYSTEM_STANDARD",      	//ϵͳ�궨
    "E_NAV_STATUS_IN_NAV",                  //��������
    "E_NAV_STATUS_STOP"                   	//ֹͣ����
};

static char* g_NavStandardStaTxt[E_NAV_STANDARD_TOTAL] =
{
    "E_NAV_STANDARD_NO_PROCCSS" ,      	    //0:δ�궨��� 
    "E_NAV_STANDARD_PROCCSSING",			//1���궨��
    "E_NAV_STANDARD_PROCCSSED",     		//2���궨���
};

static char* g_NavFusionSourceStaTxt[E_FUNSION_TOTAL] =
{
    "E_FUNSION_NONE",						//��Ч�ں�
	"E_FUNSION_GPS",						//GPS�ں�
	"FUNSION_WHEEL",						//��wheel�ں�
	"E_FUNSION_MOTION",						//�˶�ģ��Լ��
	"E_FUNSION_HEAVE",						//升沉算法约束
};

static char* g_NavRtkStatusStaTxt[E_GPS_RTK_TOTAL] =
{
    "E_GPS_RTK_INVALID",  
	"E_GPS_RTK_SPP", 
    "E_GPS_RTK_DGPS",
    "E_GPS_RTK_UNKNOW",
    "E_GPS_RTK_FIXED",
    "E_GPS_RTK_FLOAT",
};


extern _NAV_Data_Full_t NAV_Data_Full;
extern int g_KF_UP2_gps_delay_cunt;
extern _NAV_Funsion_Status_Time_t g_NAV_Funsion_Status_Time;
extern unsigned long int g_NavIndex;
extern KalmanErrorMat_t	g_kalmanErrMat; 



#endif

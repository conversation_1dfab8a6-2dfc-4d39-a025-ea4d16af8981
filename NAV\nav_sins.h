/***********************************************************************************
nav sins module header
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-2-4          | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/
#ifndef __NAV_SINS_H__
#define __NAV_SINS_H__
//#include "NAV_MCU.h"
#include "nav_type.h"


void SINS_Init(_NAV_Data_Full_t* NAV_Data_Full_p);

void SINS_UP(_NAV_Data_Full_t* NAV_Data_Full_p);
void SINS_UP_HP(_NAV_Data_Full_t* NAV_Data_Full_p);
void Earth_UP(_NAV_Data_Full_t* NAV_Data_Full_p,double *tmp_pos, double *tmp_vn);
void SINS_UP_DR(_NAV_Data_Full_t* NAV_Data_Full_p);
void gyro_cali(_NAV_Data_Full_t* NAV_Data_Full_p);

#endif




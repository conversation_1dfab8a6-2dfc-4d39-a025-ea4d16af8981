#ifndef __NAV_TYPE_H_
#define __NAV_TYPE_H_
#include "nav_const.h"
//#include "NAV_MCU.h"
#include "algorithm.h"

////////////////////////////////////////////////////////
//������ؽṹ������
//��������ϵn�������� 
//��������ϵb����ǰ��
////////////////////////////////////////////////////////

//IMU////////////////////////////////////////////////////
typedef struct
{
	double acc_raw[3];
	double gyro_raw[3];//dps
	double gyro_fog_raw[3];
	float temp_mems_raw;
	float temp_fog_raw;
	
	double acc[3];
	double gyro[3];
	double gyro_fog[3];
	
	double acc_pre[3];
	double gyro_pre[3];
	double gyro_fog_pre[3];
	
	float temp_mems;
	float temp_fog;
	
	double acc_use[3];//m/s2
	double gyro_use[3];//rad/s
	double acc_use_pre[3];
	double gyro_use_pre[3];
	double acc_use_kalman[3];
	double gyro_use_kalman[3];
	double acc_init[3];
	double angle_acc_mag;
	float temp_use;	
}_IMU_t;
//GPS////////////////////////////////////////////////////
typedef struct
{
//	double pos[3];
//	double vn[3];
//	double att[3];
	double Lat;//��λ��
	double Lon;//��λ��
	double Altitude;//��λm
	double ve;//m/s
	double vn;
	double vu;
    double Pitch;
    double Roll;
    double Heading;
	double trackTrue;//������
	
	unsigned char Position_Status;
	unsigned char rtkStatus;   //kinematic 0: �޶�λ 1�����㶨λ 2����ֽ� 3��  4��RTK�̶��� 5��RTK������
	unsigned char headingStatus;//movingbase 0: �޶�λ 1�����㶨λ 2����ֽ� 3��  4��RTK�̶��� 5��RTK������
	unsigned char pre_rtkStatus; 
    unsigned char Sate_Num;	
    unsigned int	gpsweek;
    unsigned int 		gpssecond;//��������msΪ��λ
	unsigned int        gpssecond_old;
    float delay_pps;     //gps��ʱ��ms
	float delay; 
	unsigned char gps_up_flag;
	
}_GPS_t;

//Heave Constraint Parameters for Marine Applications//////////
typedef struct
{
	double wave_detect_acc_threshold;		//波浪检测加速度阈值 m/s2
	double wave_detect_gyro_threshold;		//波浪检测角速度阈值 rad/s
	double wave_detect_duration_threshold;	//波浪检测持续时间阈值 s
	double heave_constraint_strength;		//升沉约束强度系数
	double heading_constraint_strength;		//航向角约束强度系数
	double velocity_constraint_noise[3];	//速度约束噪声 [x,y,z]
	double heading_constraint_noise;		//航向角约束噪声
	unsigned char enable_heave_mode;		//是否启用升沉模式 0:禁用 1:启用
	unsigned char wave_motion_detected;		//波浪运动检测标志
	double wave_motion_timer;				//波浪运动检测计时器
}_HeaveConstraint_t;

//Param//////////////////////////////////////////////////
typedef struct
{
	double gnssArmLength[3];
	double gyro_off[3];//rad/s
	double acc_off[3];//m/s2
	double gnssAtt_from_vehicle[3];//˫�������IMU�İ�װ�Ƕ�
	double gnssAtt_from_vehicle2[3];//˫�������IMU�İ�װ�Ƕ�,��λ��
	double OD_ArmLength[3];
	double OD_Att_from_vehicle[3];//���������IMU�İ�װ�Ƕ�
	unsigned char test_flag[7];
	double wb_set[3];
	//double scale_factor;
	//double scale_factor_filte;
	_HeaveConstraint_t heave_params;		//升沉约束参数

}_Param_t;//



//earth//////////////////////////////////////////////////
typedef struct{
	//double Re0;
	double sL, cL, tL;//sinL cosL tanL,LΪά��ֵ
	double RMh, RNh,clRNh;
	double Wie;	
	//wnie�����崦������ת����Ľ��ٶ�
    //wnen:����������ϵ�½��ٶ�
    //wnin���������ϵ�½��ٶ�
    //wnin=wnen+wnie
	double wnie[3],wnen[3],wnin[3];
	double wnien[3];//wnie+wnin
	//double g;
	double gn[3];
	double gcc[3];

}_EARTH_t;
//SINS////////////////////////////////////////////////////
typedef struct
{	
	double att[3];	  //��̬ ���������������,��λrad
	double att_deg[3];////��̬ ���������������,��λDEG
	double att_bias[3];//��ʼ��̬ƫ��
    double att_pre[3];	
	double vn[3];	   //��Ե�������ϵ���ٶ�
	double pos[3];	  //λ�� γ(rad)����(rad)���ߣ�����m��
	double wb_ib[3];//iϵ�²�����Ľ��ٶ�
	double fb_ib[3];	  //iϵ�²�����ļӼ�m/s2
//	double wb[3];	   //��������
//	double fb[3];	  //�ӱ�����
    double Cb2n[3*3],Cn2b[3*3];
    double qnb[4];	
	double eb[3];	   //������ƫ,��λrad/s
	double db[3];	   //�ӱ���ƫ,��λm/s2
	double gyro[3],acc[3];           //ʹ�õ����ݡ��ӱ�����
	double gyro_pre[3],acc_pre[3];   //ʹ�õ����ݡ��ӱ���ʷ����
	double gyro_off[3];   	//��λrad/s
	double acc_off[3];
	double fn[3];//����ϵ����������λm/s2, ���ϵ�ǰ�������ٶȾ��ǵ�ǰ���ٶ�
    double an[3];//����ϵ�µ�ǰ���ٶȵ�λm/s2
	double web[3],wnb[3],wnb_pre[3];//web����������ϵ�µĽ��ٶ�,wnb����ϵ�µĽ��ٶ�
	unsigned char Init_flag;
	float ts, nts;//ts�Ǵ������Ĳ������ڣ�nts���㷨�����Ĳ�������
	double Mpv[9];//λ�ø��¾���
//	_Mat_t Cnb,Cbn;
//	_Q_t qnb;
	double q_Norm;
	double q_Norm_f;
	double dtheta[3];
	
}_SINS_t;

//SINS_buffer////////////////////////////////////////////////////
typedef struct
{	
	double att[3];	  //��̬ ���������������	
	double vn[3];	   //��Ե�������ϵ���ٶ�
	double pos[3];	  //λ�� γ�������ߣ����Σ�
    double qnb[4];	
    double wnb[3];    
	double Mpv[9];	
	double an[3];
}_SINS_BUFFER_t;


//ODS////////////////////////////////////////////////////
typedef struct
{
	unsigned int counter;                   /*������ʱ���*/
	unsigned int counter_old;
	float timestamp;					/* ʱ���, ��λ:s , ���ȣ�0.0001*/
	float timestamp_old; 
	float WheelSpeed_ave;	
    float WheelSpeed_Front_Left;		/* ���� ��ǰ, ��λ: m/s, ���ȣ�����*/    
    float WheelSpeed_Back_Left;			/* ���� ���, ��λ: m/s, ���ȣ�����*/
    float WheelSpeed_Front_Right;		/* ���� ��ǰ, ��λ: m/s, ���ȣ�����*/
    float WheelSpeed_Back_Right;		/* ���� �Һ�, ��λ: m/s, ���ȣ�����*/
    float WheelSteer;					/* ������, ��λ: ��, ���ȣ�����*/
    float OdoPulse_1;					/* ��̼�����, ��λ:  ����, ����: ���� */
    float OdoPulse_2;					/* ��̼�����, ��λ:  ����, ����: ���� */
    unsigned char Gear;						/* ������λ */

	double att_ods2_b[3];
	double att_ods2_b_filte[3];
	
	double scale_factor;
	double scale_factor_filte;
	unsigned char ods_flag;  //1:�����ټ�  0�������ټ�
	unsigned char ods_caculat_flag;
	float    deltaT;
	
	
	
}_OD_t;

//KF////////////////////////////////////////////////////
typedef struct
{
	//15�����ֱ�Ϊ��̬����(ʧ׼��)���ٶ���ά�Ⱦ��ȸ߳���������ƫ���Ӽ���ƫ���
	double Xk[NA];
	double Xk_[NA];
	double Zk[7];
	double Qk[NA*NA];
	double RK[7];
	double Fk[NA*NA];//̬���ٶ���λ�������ٶȼ���ƫ����������ƫ
	double Pxk[NA*NA];//p k ;  p k-1
	double Pxk_[NA*NA];//p k,k-1
	double Kk[NA*7];	
	double P_max[NA];
	double P_min[NA];
	unsigned char measure_flag;
	unsigned char measure_flag_vn;
	unsigned char measure_flag_pos;
	unsigned char measure_flag_head;  //1����˫���ߺ���� 0������˫���ߺ����
	unsigned char RTK_Heading_OK;
    unsigned char use_gps_flag; //1����GPS 0:����gps�� ���õ��ں�״̬

	unsigned char pre_fusion_source;
	unsigned char fusion_source; //1��gps 2������ �� ʵ�ʵ��ں�״̬
	
	
	unsigned int cunt;
	unsigned char step;
}_KF_t;

//����������
typedef struct
{
	double mag_raw[3];
	double mag_cali[3];//Բ�Ĺ�0��0��0
	double mag_use[3];//��׼Բ
	double mag_use_pre[3];//����ǰһʱ�̴�����
	double mag_dec;//��ƫ��
	double mag_err_pre1;//�����������������
	double mag_err_pre2;
	double mag_use_buffer[10];//�������������
	int magbuff_p;//�����ƻ�����ָ��
	double x;
	double y;
	double z;
	double a;
	double b;
	double c;
	double mag_init[3];//������raw��ʼֵ
	double mag_init2[3];//������use��ʼֵ
	double mag_cali_use;//������У��ʹ��ֵ
	char mag_fastflag;//����У����־λ0:δУ����1��У�����
	double mag_angle;//�����ƽǶȱ仯
}_MAGNET_T;
//��������Ϣ
typedef struct
{
	char magbuff_flag;//�����ƻ����������Ϊ1
	int  index;
}_MAGInfo_T;
//�����ƾ���
typedef struct
{	
	int MAGNET_buffer[MAG_BUFFER_SIZE*3];
	double MagK[MAG_BUFFER_SIZE*6];//[y^2,z^2,X,Y,Z,1]
	double MagY[MAG_BUFFER_SIZE];//-x^2
	double MagX[6];//A,B,C,D,E,F
	double Magoff[6];//Բ�ĺ�����
}_MAG_buff_T;


typedef struct
{
	_IMU_t IMU;
	_GPS_t GPS;
	_MAGNET_T MAGNET;
	_Param_t Param;
	_MAG_buff_T MAGNET_BUFF;
	_MAGInfo_T magbuff_info;//��ʾ�������Ѿ�����
	_EARTH_t EARTH;
	_SINS_t SINS;
	_SINS_BUFFER_t SINS_buffer[SINS_BUFFER_SIZE];
	unsigned char Head,end;
	_OD_t ODS;
	_KF_t KF;
	unsigned char pre_Nav_Standard_flag;//��һ���궨״̬
	unsigned char Nav_Standard_flag;  //0:�궨δ���  1���궨���
	unsigned char pre_Nav_Status;//�ϸ�״̬
	unsigned char Nav_Status;  //0: ����׼��  1��SINS��ʼ��׼ 2��SINS KF��ʼ�� 3��ϵͳ�궨 4���������� 5��ֹͣ����
	unsigned char Gnss_Use_Status;//E_NAV_SUPPORT_RTK_FIEX_STATUS:ֻ֧��rtk����й۲⣻E_NAV_SUPPORT_RTK_ALL_STATUS:֧��SPP��DGPS��FIEXED��FLOAT���Լ��

	unsigned short debug: 1;		//0:normal 1:debug mode
    unsigned short imuSelect: 1;	//0:mems 1:ifog
	unsigned short memsType: 2;	//0: imu460 1:scha63x
	unsigned short mag_cali_flag: 1; //�����Ʊ궨λ 0������Ҫ���±궨��1����Ҫ���±궨

	unsigned char  UseCase;		//ʹ�ó�����0��Ĭ�ϳ���ʹ������+GNSS+ODS 1:�������˻�

	double difAng;
	_calib_t				gyroCalib;
	_calib_t				accCalib;
}_NAV_Data_Full_t;

typedef struct
{
	unsigned char  fusion_source;
	unsigned int   duration;
}_NAV_Funsion_Status_Time_t;

//IIR�˲����ṹ��
typedef struct
{
	double a;
	double b;
	double x;
	double y;
}_IIR_Filter_t;

//�����˲��ṹ��
typedef struct
{
	_IIR_Filter_t  IIR_Filter;
	unsigned char  adaptive;//�Ƿ��������Ӧ��ʽ0�������ã�1������
	double Kp;
	double Ki;
	double Kd;
}_NAV_MAHONY_t;





#endif 




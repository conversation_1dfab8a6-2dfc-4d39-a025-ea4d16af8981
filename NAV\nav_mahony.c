/***********************************************************************************
nav Mahony module
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-5-9          | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/
#include <string.h>
#include "nav_includes.h"
double angle_mag = 0;

_NAV_MAHONY_t  g_NAV_MAHONY={0};

//IIR滤波器
//Butterworth lowpass IIR filter
//设计为4阶低通滤波器
void IIRFilter()
{
	
}

//线性插值
//X为两点横坐标，Y为两点纵坐标，value插值点横坐标，返回插值点纵坐标
double interplim(double *X,double *Y, double value)
{
	double kk;
	if(X[1]==X[0])
	{
#ifdef linux
		inav_log(INAVMD(LOG_ERR),"X[0]==X[1]=%f\r\n",X[0]);
#endif
		return Y[0];
	}
	kk=(Y[1]-Y[0])/(X[1]-X[0]);
	//保证value在X中间
	if(		(value>=X[0]&&value<X[1])
		||	(value<X[0]&&value>=X[1])
		)
	{
		return Y[0]+kk*(value-X[0]);
	}
	else
	{
#ifdef linux
		inav_log(INAVMD(LOG_ERR),"value is not between X[0] and X[1]\r\n");
#endif
		return Y[0]+kk*(value-X[0]);
	}
}

void SetPidParmByMotion(_NAV_Data_Full_t* NAV_Data_Full_p,_NAV_MAHONY_t *pNAV_MAHONY)
{
	_IIR_Filter_t mIIR_Filter={0};
	if(E_AHRS_ADAPTIVE_YES == pNAV_MAHONY->adaptive)
	{
		//计算加计误差
		//对加计进行IIR滤波
		
		
	}
	else if(E_AHRS_ADAPTIVE_NO == pNAV_MAHONY->adaptive)
	{
	}
	else
	{
#ifdef linux
		inav_log(INAVMD(LOG_ERR),"Error g_NAV_MAHONY->adaptive=%d\r\n",pNAV_MAHONY->adaptive);
#endif
	}
}
void MahonyInit(_NAV_MAHONY_t *pNAV_MAHONY)
{
	memset(pNAV_MAHONY,0,sizeof(_NAV_MAHONY_t));
}
void MagDataUse(_NAV_Data_Full_t* NAV_Data_Full_p)//判断磁力计数据是否可用
{
	
}
void MahonyUpdate(_NAV_Data_Full_t* NAV_Data_Full_p)
{
#if 0
	double phim[3]={0.0};
	//计算姿态
	for(i=0;i<3;i++)
	{
		phim[i]=NAV_Data_Full_p->IMU.gyro_use[i]*NAV_Data_Full_p->SINS.ts;//rad
	}
	SINS_UP_ATT(NAV_Data_Full_p,phim);
#endif	
	int i=0;
	static double normag = 0;
	char use_mag = 0;
	//判断磁力计与上一时刻的差距，差距大判断有干扰
//	if(fabs(normag - norm(NAV_Data_Full_p->MAGNET.mag_use, 3))<0.01
//		&&fabs(NAV_Data_Full_p->MAGNET.mag_use[0] - NAV_Data_Full_p->MAGNET.mag_use_pre[0]) < 0.01
//		&&fabs(NAV_Data_Full_p->MAGNET.mag_use[1] - NAV_Data_Full_p->MAGNET.mag_use_pre[1]) < 0.01
//		&&fabs(NAV_Data_Full_p->MAGNET.mag_use[2] - NAV_Data_Full_p->MAGNET.mag_use_pre[2]) < 0.01
//	)
//	{
//		use_mag = 1;
//	}
	
	normag = norm(NAV_Data_Full_p->MAGNET.mag_use, 3);
	double tmp_acc[3]={0};
	static double err_acc[3]={0};
	//double err_acc_norm[3]={0};
	double temp1[3] = {0};
	double temp2[3] = {0};
	/**********************计算加速度计修正量***********************************************************/
	//由于导航系N是东北天，机体系为右前上，
	//如果加速度计坐标系与机体系一致，
	//载体静止且水平时加速度计测量值应为[0,0,gn]，
	//Z方向为正（由于加速度计测的实际是1g的支持力，而非重力）。
	//利用Cnb矩阵将地球矢量转到机体系右前上，得到vx, vy, vz。
	matmul("NN", 3, 1, 3, 1.0, NAV_Data_Full_p->SINS.Cn2b, NAV_Data_Full_p->EARTH.gn, 0.0, tmp_acc);
	//将实际输出加计与tmp_acc求向量积
	
	normv3(tmp_acc,temp1);
	normv3(NAV_Data_Full_p->IMU.acc_use_pre,temp2);
	cross3(temp1,temp2,err_acc);
	//归一化

#if 0
	normv3(err_acc,err_acc_norm);

#endif	
	
	double tmp_mag[3]={0};
	double tmp1_mag[3]={0};
	double tmp2_mag[3]={0};
	static double err_mag[3]={0};
	//double err_mag_norm[3]={0};
	
	/**************************计算磁力计修正量********************************************************/
	//如果不考虑误差，磁力计的测量值经过正确Cbn转到地理系，理论上水平方向上只有北向有值，
	//因此地球磁场的切线在南北方向上。
	//但实际上由于航向不准，造成Cbn有误差，所以转换后的磁力计测量值在北向和东向都有值。
	//互补滤波算法中，先将磁力计的值用Cbn转到地理系：
#if 1
	//磁力计转单位向量
	normv3(NAV_Data_Full_p->MAGNET.mag_use,temp1);
	matmul("NN", 3, 1, 3, 1.0, NAV_Data_Full_p->SINS.Cb2n, temp1, 0.0, tmp_mag);
	//由于在水平方向，无论Cbn在航向上有没有误差
	//转换后水平方向矢量和应该相等。
	//-3.6667深圳磁偏角
	if (1)
	{
	tmp1_mag[0] = sin(NAV_Data_Full_p->MAGNET.mag_dec * DEG2RAD - NAV_Data_Full_p->SINS.att_bias[2])*sqrt(tmp_mag[0]*tmp_mag[0]+tmp_mag[1]*tmp_mag[1]);
	tmp1_mag[1] = -sqrt(tmp_mag[0]*tmp_mag[0]+tmp_mag[1]*tmp_mag[1]) * cos(NAV_Data_Full_p->MAGNET.mag_dec * DEG2RAD - NAV_Data_Full_p->SINS.att_bias[2]) ;
	tmp1_mag[2] = -tmp_mag[2];
	}
	else 
	{
	tmp1_mag[0] = 0;
	tmp1_mag[1] = -sqrt(tmp_mag[0]*tmp_mag[0]+tmp_mag[1]*tmp_mag[1]);
	tmp1_mag[2] = -tmp_mag[2];

	}
	
	//再转回载体坐标系
	matmul("NN", 3, 1, 3, 1.0, NAV_Data_Full_p->SINS.Cn2b, tmp1_mag, 0.0, tmp2_mag);
	//将实际输出与tmp2_mag做向量积
	cross3(tmp2_mag,temp1,err_mag);
	//normv3(err_mag,err_mag_norm);

	

#endif
	//MagInitHeading(imu_M_sum,NAV_Data_Full_p->SINS.att,NAV_Data_Full_p->SINS.pos);
	//double kp=0.0;
	//double ki=0.0;
	double err_both[3]={0.0};
	double deltap[3]={0.0};
	double deltai[3]={0.0};
	double deltad[3]={0.0};
	/*************************修正陀螺仪输出值**********************************************************/
	err_acc[2] = 0;
	err_mag[0] = 0;
	err_mag[1] = 0;
	
	if(err_mag[2]>0.2 || use_mag == 0)//磁力计与当前航向误差过大，认为磁力计受到干扰，不采用
	{
		err_mag[2] = 0;
	}
	if(err_acc[0]>0.1 || err_acc[1]>0.1)//判断当前载体正在做加速或减速运动，不使用加计更新
	{
		err_acc[0] = 0;
		err_acc[1] = 0;
	}
	
	NAV_Data_Full_p->MAGNET.mag_err_pre1 = NAV_Data_Full_p->MAGNET.mag_err_pre2;
	NAV_Data_Full_p->MAGNET.mag_err_pre2 = err_mag[2];
	if (fabs(NAV_Data_Full_p->MAGNET.mag_err_pre1) < 0.01 
		||fabs(NAV_Data_Full_p->MAGNET.mag_err_pre2) < 0.01 )
	{
		err_mag[2] = err_mag[2] * 0.5;
	}

	err_mag[2] = err_mag[2]*err_mag[2]*50*err_mag[2]*100;//磁力计误差大，调节回正和静止摇摆幅度

	//test = norm(NAV_Data_Full_p->IMU.acc_use_pre,3) - fabs(NAV_Data_Full_p->EARTH.gn[2]);
	static double temp_angle = 0;
	temp_angle = acos(-(NAV_Data_Full_p->IMU.acc_use_pre[0]*NAV_Data_Full_p->MAGNET.mag_use[0]
		+NAV_Data_Full_p->IMU.acc_use_pre[1]*NAV_Data_Full_p->MAGNET.mag_use[1]
	+NAV_Data_Full_p->IMU.acc_use_pre[2]*NAV_Data_Full_p->MAGNET.mag_use[2])/norm(NAV_Data_Full_p->IMU.acc_use_pre,3)/norm(NAV_Data_Full_p->MAGNET.mag_use,3))*RAD2DEG;
	if (fabs(temp_angle -NAV_Data_Full_p->IMU.angle_acc_mag) > 3)//载体加速状态不使用加计更新，磁力计磁力干扰时不用磁力计更新
	{	
		if(fabs(normag-1)<0.03)//判断磁力计是否受到外部干扰，如磁力计未受到干扰则判断为载体加速
		{
			//err_acc[0] = 0;
			//err_acc[1] = 0;
			err_mag[2] = 0;
		}
		else
		{
			err_mag[2] = 0;
		}
		
	}
	if (fabs(temp_angle -NAV_Data_Full_p->IMU.angle_acc_mag) > 20)
	{
		//err_acc[0] = 0;
		//err_acc[1] = 0;
	}
	if (fabs(normag-1)>0.05
		||fabs(NAV_Data_Full_p->IMU.gyro_use[2]*RAD2DEG) >5 
		//||NAV_Data_Full_p->MAGNET.mag_fastflag ==0
		||NAV_Data_Full.mag_cali_flag == 1)
	{
		err_mag[2] = 0;
	}

	err_mag[2] = 0;
	matrixSum(err_acc, err_mag, 3, 1, 1.0, err_both);
	//根据pi调节，设置对陀螺测量值的修正量：
	//根据当前运动情况,设置kp,ki
	//SetPidParmByMotion(NAV_Data_Full_p,&g_NAV_MAHONY);
	double tau=2;
	g_NAV_MAHONY.Kp=2*(2.146/(tau));
	g_NAV_MAHONY.Ki=2*2.146/tau;
	//g_NAV_MAHONY.Kd=3*(2.146/tau);
	//P调节修正量
	
#if 1	

	for(i=0;i<3;i++)
	{
		deltap[i]=g_NAV_MAHONY.Kp*err_both[i];
	}
	//调节修正
	//i调节修正量
	for(i=0;i<3;i++)
	{
		deltai[i]=g_NAV_MAHONY.Ki*err_both[i]*NAV_Data_Full_p->SINS.ts;
	}
	//d调节修正量
	for(i=0;i<3;i++)
	{
		//deltad[i]=g_NAV_MAHONY.Kd*NAV_Data_Full_p->IMU.gyro_use[i]*DEG2RAD;
	}	
	//比例对陀螺仪测量值进行修正：
	/*
	for(i=0;i<3;i++)
	{
	 	NAV_Data_Full_p->SINS.att[i]=NAV_Data_Full_p->SINS.att[i]+deltap[i];
	}
	//积分数据对陀螺仪测量值进行修正：
	for(i=0;i<3;i++)
	{
	 	NAV_Data_Full_p->SINS.att[i]=NAV_Data_Full_p->SINS.att[i]+deltai[i];
	}
	//微分数据对陀螺测量值修正
	
	for(i=0;i<3;i++)
	{
	 	NAV_Data_Full_p->SINS.att[i]=NAV_Data_Full_p->SINS.att[i]+deltad[i];	
	}
	*/
	//防止加速引起的p值过大
	if(deltap[0]>0.01)
	{
		deltap[0] = 0.01;
	}
	if(deltap[1] > 0.01)
	{
		deltap[1] = 0.01;
	}
	if(deltap[0] < -0.01)
	{
		deltap[0] = -0.01;
	}
	if(deltap[1]< -0.01)
	{
		deltap[1] = -0.01;
	}
	if(deltap[2] > 0.05)
	{
		deltap[2] = 0.05;
	}
	if(deltap[2]< -0.05)
	{
		deltap[2] = -0.05;
	}
	
#endif
	//NAV_Data_Full_p->SINS.att[3]=NAV_Data_Full_p->SINS.att[3]+deltap[3];
	//NAV_Data_Full_p->SINS.att[3]=NAV_Data_Full_p->SINS.att[3]+deltai[3];
	//NAV_Data_Full_p->SINS.att[3]=NAV_Data_Full_p->SINS.att[3]+deltad[3];

	//四元数更新角增量
	
	
	double nts = NAV_Data_Full_p->SINS.nts; //0.005
	double nts_2 = nts*nts;
	double gyro[3], acc[3];
	double wib[3], fb[3],wie_b[3],win_b[3];
	double Cnb[3 * 3];
	double wb_set[3] = {0*DEG2RAD,0*DEG2RAD,0*DEG2RAD};
    double db_set[3] = {0,0,0};

	//NAV_Data_Full_p->IMU.gyro_use[2] = 0;
	//deltap[2] = 0;
	
	double calib[3] = {0};
	gyro[0] = NAV_Data_Full_p->IMU.gyro_use[0] + wb_set[0];
	gyro[1] = NAV_Data_Full_p->IMU.gyro_use[1] + wb_set[1];
	gyro[2] = NAV_Data_Full_p->IMU.gyro_use[2] + wb_set[2];
	calib[0] = deltap[0]+deltai[0]+deltad[0];
	calib[1] = deltap[1]+deltai[1]+deltad[1];
	calib[2] = deltap[2]+deltai[2]+deltad[2];
	calib[2] = 0;
#if 0
	if (fabs(NAV_Data_Full_p->IMU.gyro_use[0]) < 0.05*DEG2RAD)
	{
		gyro[0] = gyro[0]-NAV_Data_Full_p->IMU.gyro_use[0];
	}
	if (fabs(NAV_Data_Full_p->IMU.gyro_use[1]) < 0.05*DEG2RAD)
	{
		gyro[1] = gyro[1]-NAV_Data_Full_p->IMU.gyro_use[1];
	}
	if (fabs(NAV_Data_Full_p->IMU.gyro_use[2]) < 0.05*DEG2RAD)
	{
		gyro[2] = gyro[2]-NAV_Data_Full_p->IMU.gyro_use[2];
	}
#endif
	acc[0] = NAV_Data_Full_p->IMU.acc_use[0] + db_set[0];
	acc[1] = NAV_Data_Full_p->IMU.acc_use[1] + db_set[1];
	acc[2] = NAV_Data_Full_p->IMU.acc_use[2] + db_set[2];	
	for (i = 0; i < 3; i++) 
	{
		gyro[i] = gyro[i] -NAV_Data_Full_p->SINS.eb[i];   //rad/s
		acc[i] = acc[i] - NAV_Data_Full_p->SINS.db[i];  //m/s^2	
	}
//------------------------------------------------------------------------------------
	for (i = 0; i < 3; i++) 
	{
		//NAV_Data_Full_p->SINS.wb_ib[i] = gyro[i];
		//NAV_Data_Full_p->SINS.fb_ib[i] = acc[i];
		NAV_Data_Full_p->SINS.wnb_pre[i] = NAV_Data_Full_p->SINS.wnb[i];
		NAV_Data_Full_p->SINS.wnb[i] = gyro[i];
		
	}
	
	
	if(0)
{
	//Earth_UP(NAV_Data_Full_p,NAV_Data_Full_p->SINS.pos, NAV_Data_Full_p->SINS.vn);//更新大地数据

	//Qnb2Cnb(NAV_Data_Full_p->SINS.qnb, Cnb);
	//计算载体坐标系相对大地坐标的角速度补偿
	//matmul("TN", 3, 1, 3, 1, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->EARTH.wnie,0.0,wie_b);
   //计算载体坐标系相对导航坐标系的角速度补偿
	matmul("TN", 3, 1, 3, 1, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->EARTH.wnin, 0.0, win_b);
   //计算导航坐标系下的加速度，尚未补偿重力加速度
	matmul("NN", 3, 1, 3, 1, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->SINS.fb_ib,0.0,NAV_Data_Full_p->SINS.fn);
	
	//载体相对导航系的加速度计算
    for(i = 0;i<3;i++)
	{   
		wie_b[i] = 0;
		win_b[i] = 0;
	    //计算大地坐标系下的角速度
		NAV_Data_Full_p->SINS.web[i] = NAV_Data_Full_p->SINS.wb_ib[i]- wie_b[i];
		//计算导航系下的角速度
		NAV_Data_Full_p->SINS.wnb[i] = NAV_Data_Full_p->SINS.wb_ib[i]- win_b[i];
		//计算导航系下的加速度，补偿上重力加速度m/s2,举例{-4.0409829786878522e-07, 2.9398186206018674e-07, -9.7879837185144272}
		//
		//NAV_Data_Full_p->SINS.an[i] = NAV_Data_Full_p->SINS.fn[i] + NAV_Data_Full_p->EARTH.gcc[i];
	}
	
}
	if(fabs(NAV_Data_Full_p->IMU.gyro_use[0])<0.15 * DEG2RAD
		&&fabs(NAV_Data_Full_p->IMU.gyro_use[1])<0.15 * DEG2RAD
		&&fabs(NAV_Data_Full_p->IMU.gyro_use[2])<0.15 * DEG2RAD
	)
	{
		NAV_Data_Full_p->SINS.wnb[0] = 0;
		NAV_Data_Full_p->SINS.wnb[1] = 0;
		NAV_Data_Full_p->SINS.wnb[2] = 0;
		calib[0] = 0;
		calib[1] = 0;
		calib[2] = 0;
	}
	//单子样加前一周期求等效旋转矢量
	
	double phi[3] = {0};
	for (i = 0; i < 3; i++) 
	{
		phi[i] =  NAV_Data_Full_p->SINS.wnb[i];
	}
if(1)
{
	cross3(NAV_Data_Full_p->SINS.wnb_pre, NAV_Data_Full_p->SINS.wnb, phi);
	for (i = 0; i < 3; i++) 
	{
		phi[i] =  NAV_Data_Full_p->SINS.wnb[i]*nts + phi[i]/12*nts*nts;
	}
}

	
	//姿态更新中间变量
  	double theta_2 = 0;
	double temp = 0;	
	double temp_V4[4] = {0,0,0,0};
	//四阶毕卡姿态更新		，陀螺更新
	{
		for(i = 0;i<3;i++)
		{		
			if(1)
				NAV_Data_Full_p->SINS.dtheta[i] = phi[i];
			else
				NAV_Data_Full_p->SINS.dtheta[i] = NAV_Data_Full_p->SINS.wnb[i] * nts;
		}
		//
		{
			temp = 0;
			for(i = 0;i<3;i++)
			{
					temp += NAV_Data_Full_p->SINS.dtheta[i]*NAV_Data_Full_p->SINS.dtheta[i];
			}
		}
		theta_2 = temp;
		
		temp = 0.5-0.02083*theta_2;  
		for(i = 0;i<4;i++)
		{
			if(i == 0)
			{
					temp_V4[0] = 1-0.125*theta_2+0.002604*theta_2*theta_2;
			}
			else
			{
					temp_V4[i] = NAV_Data_Full_p->SINS.dtheta[i-1]*temp;
			}
		}
	}
	//Q_up
	{
			//q_k1 = M'*q_k
			NAV_Data_Full_p->SINS.qnb[0] = temp_V4[0]*NAV_Data_Full_p->SINS.qnb[0] - temp_V4[1]*NAV_Data_Full_p->SINS.qnb[1] - temp_V4[2]*NAV_Data_Full_p->SINS.qnb[2] - temp_V4[3]*NAV_Data_Full_p->SINS.qnb[3];
			NAV_Data_Full_p->SINS.qnb[1] = temp_V4[1]*NAV_Data_Full_p->SINS.qnb[0] + temp_V4[0]*NAV_Data_Full_p->SINS.qnb[1] + temp_V4[3]*NAV_Data_Full_p->SINS.qnb[2] - temp_V4[2]*NAV_Data_Full_p->SINS.qnb[3];
			NAV_Data_Full_p->SINS.qnb[2] = temp_V4[2]*NAV_Data_Full_p->SINS.qnb[0] - temp_V4[3]*NAV_Data_Full_p->SINS.qnb[1] + temp_V4[0]*NAV_Data_Full_p->SINS.qnb[2] + temp_V4[1]*NAV_Data_Full_p->SINS.qnb[3];
			NAV_Data_Full_p->SINS.qnb[3] = temp_V4[3]*NAV_Data_Full_p->SINS.qnb[0] + temp_V4[2]*NAV_Data_Full_p->SINS.qnb[1] - temp_V4[1]*NAV_Data_Full_p->SINS.qnb[2] + temp_V4[0]*NAV_Data_Full_p->SINS.qnb[3];
	}
	//四元数归一化
	{
			NAV_Data_Full_p->SINS.q_Norm = sqrt(    NAV_Data_Full_p->SINS.qnb[0]*NAV_Data_Full_p->SINS.qnb[0]
											+ NAV_Data_Full_p->SINS.qnb[1]*NAV_Data_Full_p->SINS.qnb[1]
											+ NAV_Data_Full_p->SINS.qnb[2]*NAV_Data_Full_p->SINS.qnb[2]
											+ NAV_Data_Full_p->SINS.qnb[3]*NAV_Data_Full_p->SINS.qnb[3]
										);
			NAV_Data_Full_p->SINS.q_Norm_f = 1/NAV_Data_Full_p->SINS.q_Norm;
			NAV_Data_Full_p->SINS.qnb[0] = NAV_Data_Full_p->SINS.qnb[0]*NAV_Data_Full_p->SINS.q_Norm_f;
			NAV_Data_Full_p->SINS.qnb[1] = NAV_Data_Full_p->SINS.qnb[1]*NAV_Data_Full_p->SINS.q_Norm_f;
			NAV_Data_Full_p->SINS.qnb[2] = NAV_Data_Full_p->SINS.qnb[2]*NAV_Data_Full_p->SINS.q_Norm_f;
			NAV_Data_Full_p->SINS.qnb[3] = NAV_Data_Full_p->SINS.qnb[3]*NAV_Data_Full_p->SINS.q_Norm_f;
	}
	qnb2att(NAV_Data_Full_p->SINS.qnb, NAV_Data_Full_p->SINS.att);
	
	//四阶毕卡姿态更新		 加速度计观测误差
	if(fabs(NAV_Data_Full_p->IMU.gyro_use[2])<10 * DEG2RAD)
	
	{
		for(i = 0;i<3;i++)
		{
				NAV_Data_Full_p->SINS.dtheta[i] = calib[i]*nts;
		}
		//
		{
			temp = 0;
			for(i = 0;i<3;i++)
			{
					temp += NAV_Data_Full_p->SINS.dtheta[i]*NAV_Data_Full_p->SINS.dtheta[i];
			}
		}
		theta_2 = temp;
		
		temp = 0.5-0.02083*theta_2;  
		for(i = 0;i<4;i++)
		{
			if(i == 0)
			{
					temp_V4[0] = 1-0.125*theta_2+0.002604*theta_2*theta_2;
			}
			else
			{
					temp_V4[i] = NAV_Data_Full_p->SINS.dtheta[i-1]*temp;
			}
		}
	
	//Q_up
	{
			//q_k1 = M'*q_k
			NAV_Data_Full_p->SINS.qnb[0] = temp_V4[0]*NAV_Data_Full_p->SINS.qnb[0] - temp_V4[1]*NAV_Data_Full_p->SINS.qnb[1] - temp_V4[2]*NAV_Data_Full_p->SINS.qnb[2] - temp_V4[3]*NAV_Data_Full_p->SINS.qnb[3];
			NAV_Data_Full_p->SINS.qnb[1] = temp_V4[1]*NAV_Data_Full_p->SINS.qnb[0] + temp_V4[0]*NAV_Data_Full_p->SINS.qnb[1] + temp_V4[3]*NAV_Data_Full_p->SINS.qnb[2] - temp_V4[2]*NAV_Data_Full_p->SINS.qnb[3];
			NAV_Data_Full_p->SINS.qnb[2] = temp_V4[2]*NAV_Data_Full_p->SINS.qnb[0] - temp_V4[3]*NAV_Data_Full_p->SINS.qnb[1] + temp_V4[0]*NAV_Data_Full_p->SINS.qnb[2] + temp_V4[1]*NAV_Data_Full_p->SINS.qnb[3];
			NAV_Data_Full_p->SINS.qnb[3] = temp_V4[3]*NAV_Data_Full_p->SINS.qnb[0] + temp_V4[2]*NAV_Data_Full_p->SINS.qnb[1] - temp_V4[1]*NAV_Data_Full_p->SINS.qnb[2] + temp_V4[0]*NAV_Data_Full_p->SINS.qnb[3];
	}
	//四元数归一化
	{
			NAV_Data_Full_p->SINS.q_Norm = sqrt(    NAV_Data_Full_p->SINS.qnb[0]*NAV_Data_Full_p->SINS.qnb[0]
											+ NAV_Data_Full_p->SINS.qnb[1]*NAV_Data_Full_p->SINS.qnb[1]
											+ NAV_Data_Full_p->SINS.qnb[2]*NAV_Data_Full_p->SINS.qnb[2]
											+ NAV_Data_Full_p->SINS.qnb[3]*NAV_Data_Full_p->SINS.qnb[3]
										);
			NAV_Data_Full_p->SINS.q_Norm_f = 1/NAV_Data_Full_p->SINS.q_Norm;
			NAV_Data_Full_p->SINS.qnb[0] = NAV_Data_Full_p->SINS.qnb[0]*NAV_Data_Full_p->SINS.q_Norm_f;
			NAV_Data_Full_p->SINS.qnb[1] = NAV_Data_Full_p->SINS.qnb[1]*NAV_Data_Full_p->SINS.q_Norm_f;
			NAV_Data_Full_p->SINS.qnb[2] = NAV_Data_Full_p->SINS.qnb[2]*NAV_Data_Full_p->SINS.q_Norm_f;
			NAV_Data_Full_p->SINS.qnb[3] = NAV_Data_Full_p->SINS.qnb[3]*NAV_Data_Full_p->SINS.q_Norm_f;
	}
//	double temp_ATT[3] = {0};
//	qnb2att(NAV_Data_Full_p->SINS.qnb, temp_ATT);
//	temp_ATT[2] = NAV_Data_Full_p->SINS.att[2];
//	att2qnb2(temp_ATT,NAV_Data_Full_p->SINS.qnb);
	}
	//不同姿态表示方式转换
	{
			qnb2att(NAV_Data_Full_p->SINS.qnb, NAV_Data_Full_p->SINS.att);
			Qnb2Cnb(NAV_Data_Full_p->SINS.qnb, NAV_Data_Full_p->SINS.Cb2n);
			Mat_Tr(3,NAV_Data_Full_p->SINS.Cb2n,NAV_Data_Full_p->SINS.Cn2b);
			NAV_Data_Full_p->SINS.att_deg[0] =  NAV_Data_Full_p->SINS.att[0]*RAD2DEG;
			NAV_Data_Full_p->SINS.att_deg[1] =  NAV_Data_Full_p->SINS.att[1]*RAD2DEG;
			NAV_Data_Full_p->SINS.att_deg[2] =  NAV_Data_Full_p->SINS.att[2]*RAD2DEG;
	}

	//att2qnb(NAV_Data_Full_p->SINS.att,NAV_Data_Full_p->SINS.qnb);//这种不行
	//angle_mag = MagInitHeading(NAV_Data_Full_p->MAGNET.mag_use,NAV_Data_Full_p->SINS.att,NAV_Data_Full_p->SINS.pos) * RAD2DEG;
	
	
	
}
/****************************************************************************************************
无磁力计更新
****************************************************************************************************/
void MahonyUpdate_NoMAG(_NAV_Data_Full_t* NAV_Data_Full_p)
{
	int i=0;
	double tmp_acc[3]={0};
	double err_acc[3]={0};
	
		
	/**********************计算加速度计修正量**************************/
	//由于导航系N是东北天，机体系为右前上，
	//如果加速度计坐标系与机体系一致，
	//载体静止且水平时加速度计测量值应为[0,0,gn]，
	//Z方向为正（由于加速度计测的实际是1g的支持力，而非重力）。
	//利用Cnb矩阵将地球矢量转到机体系右前上，得到vx, vy, vz。
	if(sqrt(NAV_Data_Full.IMU.acc_use[0]*NAV_Data_Full.IMU.acc_use[0]+NAV_Data_Full.IMU.acc_use[1]*NAV_Data_Full.IMU.acc_use[1]+NAV_Data_Full.IMU.acc_use[2]*NAV_Data_Full.IMU.acc_use[2])
				-sqrt(NAV_Data_Full.EARTH.gn[2]*NAV_Data_Full.EARTH.gn[2])<0.02)
	{
		matmul("NN", 3, 1, 3, 1.0, NAV_Data_Full_p->SINS.Cn2b, NAV_Data_Full_p->EARTH.gn, 0.0, tmp_acc);
	//将实际输出加计与tmp_acc求向量积
		cross3(tmp_acc,NAV_Data_Full_p->IMU.acc_use,err_acc);
	}
	
	double tmp_mag[3]={0};
	double tmp1_mag[3]={0};
	double tmp2_mag[3]={0};
	double err_mag[3]={0};
	
	/**************************计算磁力计修正量************************/
	//如果不考虑误差，磁力计的测量值经过正确Cbn转到地理系，理论上水平方向上只有北向有值，
	//因此地球磁场的切线在南北方向上。
	//但实际上由于航向不准，造成Cbn有误差，所以转换后的磁力计测量值在北向和东向都有值。
	//互补滤波算法中，先将磁力计的值用Cbn转到地理系：
	/*
	matmul("NN", 3, 1, 3, 1.0, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->MAGNET.mag_use, 0.0, tmp_mag);
	//由于在水平方向，无论Cbn在航向上有没有误差，
	//转换后水平方向矢量和应该相等。
	tmp1_mag[0]=0;
	tmp1_mag[1]=sqrt(tmp_mag[0]*tmp_mag[0]+tmp_mag[1]*tmp_mag[1]);//航向角0度时n系磁力强度
	tmp1_mag[2]=tmp_mag[2];
	//再转回载体坐标系
	matmul("NN", 3, 1, 3, 1.0, NAV_Data_Full_p->SINS.Cn2b, tmp1_mag, 0.0, tmp2_mag);//b系磁力
	//将实际输出与tmp2_mag做向量积
	cross3(tmp2_mag,NAV_Data_Full_p->MAGNET.mag_use,err_mag);
	*/
	//double kp=0.0;
	//double ki=0.0;
	double err_both[3]={0.0};
	double deltap[3]={0.0};
	double deltai[3]={0.0};
	/*************************修正陀螺仪输出值*****************************/
	//matrixSum(err_acc, err_mag, 3, 1, 1.0, err_both);
	err_both[0]=err_acc[0];
	err_both[1]=err_acc[1];
	err_both[2]=err_acc[2];
	//根据pi调节，设置对陀螺测量值的修正量：
	//根据当前运动情况,设置kp,ki
	//SetPidParmByMotion(NAV_Data_Full_p,&g_NAV_MAHONY);
	//P调节修正量
	double tau=60;
	g_NAV_MAHONY.Kp=2*(2.146/tau);
	g_NAV_MAHONY.Ki=(2.146/tau)*(2.146/tau);
	for(i=0;i<3;i++)
	{
		deltap[i]=g_NAV_MAHONY.Kp*err_both[i];
	}
	//i调节修正量
	for(i=0;i<3;i++)
	{
		deltai[i]=g_NAV_MAHONY.Ki*err_both[i]*NAV_Data_Full_p->SINS.ts;
	}	
	//对陀螺仪测量值进行修正：
	for(i=0;i<3;i++)
	{
	 	NAV_Data_Full_p->IMU.gyro_use[i]=NAV_Data_Full_p->IMU.gyro_use[i]+deltap[i]+deltai[i];
	}

	double phim[3]={0.0};
	//计算姿态
	for(i=0;i<3;i++)
	{
		phim[i]=NAV_Data_Full_p->IMU.gyro_use[i]*NAV_Data_Full_p->SINS.ts;//rad
	}
	
	{
	int i;
	double nts = NAV_Data_Full_p->SINS.nts; //0.005
	double nts_2 = nts*nts;
	double gyro[3], acc[3];
	double wib[3], fb[3],wie_b[3],win_b[3];
	double Cnb[3 * 3];
	double wb_set[3] = {0*DEG2RAD,0*DEG2RAD,0*DEG2RAD};
    double db_set[3] = {0,0,0};

	//姿态更新中间变量
    double theta_2 = 0;
	double temp = 0;	
	double temp_V4[4] = {0,0,0,0};
	gyro[0] = NAV_Data_Full_p->IMU.gyro_use[0] + wb_set[0];
		gyro[1] = NAV_Data_Full_p->IMU.gyro_use[1] + wb_set[1];
		gyro[2] = NAV_Data_Full_p->IMU.gyro_use[2] + wb_set[2];
		acc[0] = NAV_Data_Full_p->IMU.acc_use[0] + db_set[0];
		acc[1] = NAV_Data_Full_p->IMU.acc_use[1] + db_set[1];
		acc[2] = NAV_Data_Full_p->IMU.acc_use[2] + db_set[2];	
	
			
			{
					for(i = 0;i<3;i++)
					{
							NAV_Data_Full_p->SINS.dtheta[i] = NAV_Data_Full_p->SINS.wnb[i]*nts;
					}
					//
					{
							temp = 0;
							for(i = 0;i<3;i++)
							{
									temp += NAV_Data_Full_p->SINS.dtheta[i]*NAV_Data_Full_p->SINS.dtheta[i];
							}
					}
					theta_2 = temp;
					
					temp = 0.5-0.02083*theta_2;  
					for(i = 0;i<4;i++)
					{
							if(i == 0)
							{
									temp_V4[0] = 1-0.125*theta_2+0.002604*theta_2*theta_2;
							}
							else
							{
									temp_V4[i] = NAV_Data_Full_p->SINS.dtheta[i-1]*temp;
							}
					}
			}
			//Q_up
			{
					//q_k1 = M'*q_k
					NAV_Data_Full_p->SINS.qnb[0] = temp_V4[0]*NAV_Data_Full_p->SINS.qnb[0] - temp_V4[1]*NAV_Data_Full_p->SINS.qnb[1] - temp_V4[2]*NAV_Data_Full_p->SINS.qnb[2] - temp_V4[3]*NAV_Data_Full_p->SINS.qnb[3];
					NAV_Data_Full_p->SINS.qnb[1] = temp_V4[1]*NAV_Data_Full_p->SINS.qnb[0] + temp_V4[0]*NAV_Data_Full_p->SINS.qnb[1] + temp_V4[3]*NAV_Data_Full_p->SINS.qnb[2] - temp_V4[2]*NAV_Data_Full_p->SINS.qnb[3];
					NAV_Data_Full_p->SINS.qnb[2] = temp_V4[2]*NAV_Data_Full_p->SINS.qnb[0] - temp_V4[3]*NAV_Data_Full_p->SINS.qnb[1] + temp_V4[0]*NAV_Data_Full_p->SINS.qnb[2] + temp_V4[1]*NAV_Data_Full_p->SINS.qnb[3];
					NAV_Data_Full_p->SINS.qnb[3] = temp_V4[3]*NAV_Data_Full_p->SINS.qnb[0] + temp_V4[2]*NAV_Data_Full_p->SINS.qnb[1] - temp_V4[1]*NAV_Data_Full_p->SINS.qnb[2] + temp_V4[0]*NAV_Data_Full_p->SINS.qnb[3];
			}
			//四元数归一化
			{
					NAV_Data_Full_p->SINS.q_Norm = sqrt(    NAV_Data_Full_p->SINS.qnb[0]*NAV_Data_Full_p->SINS.qnb[0]
													+ NAV_Data_Full_p->SINS.qnb[1]*NAV_Data_Full_p->SINS.qnb[1]
													+ NAV_Data_Full_p->SINS.qnb[2]*NAV_Data_Full_p->SINS.qnb[2]
													+ NAV_Data_Full_p->SINS.qnb[3]*NAV_Data_Full_p->SINS.qnb[3]
												);
					NAV_Data_Full_p->SINS.q_Norm_f = 1/NAV_Data_Full_p->SINS.q_Norm;
					NAV_Data_Full_p->SINS.qnb[0] = NAV_Data_Full_p->SINS.qnb[0]*NAV_Data_Full_p->SINS.q_Norm_f;
					NAV_Data_Full_p->SINS.qnb[1] = NAV_Data_Full_p->SINS.qnb[1]*NAV_Data_Full_p->SINS.q_Norm_f;
					NAV_Data_Full_p->SINS.qnb[2] = NAV_Data_Full_p->SINS.qnb[2]*NAV_Data_Full_p->SINS.q_Norm_f;
					NAV_Data_Full_p->SINS.qnb[3] = NAV_Data_Full_p->SINS.qnb[3]*NAV_Data_Full_p->SINS.q_Norm_f;
			}
			//不同姿态表示方式转换
			{
					qnb2att(NAV_Data_Full_p->SINS.qnb, NAV_Data_Full_p->SINS.att);
					Qnb2Cnb(NAV_Data_Full_p->SINS.qnb, NAV_Data_Full_p->SINS.Cb2n);
					Mat_Tr(3,NAV_Data_Full_p->SINS.Cb2n,NAV_Data_Full_p->SINS.Cn2b);
			}
	for (i = 0; i < 3; i++) 
	{
		gyro[i] = gyro[i] -NAV_Data_Full_p->SINS.eb[i];   //rad/s
		acc[i] = acc[i] - NAV_Data_Full_p->SINS.db[i];  //m/s^2	
	}
//------------------------------------------------------------------------------------
	for (i = 0; i < 3; i++) 
	{
		NAV_Data_Full_p->SINS.wb_ib[i] = gyro[i];
		NAV_Data_Full_p->SINS.fb_ib[i] = acc[i];
	}
	
	
	//Earth_UP(NAV_Data_Full_p,NAV_Data_Full_p->SINS.pos, NAV_Data_Full_p->SINS.vn);//更新大地数据

//	Qnb2Cnb(NAV_Data_Full_p->SINS.qnb, Cnb);
	//计算载体坐标系相对大地坐标的角速度补偿
	matmul("TN", 3, 1, 3, 1, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->EARTH.wnie,0.0,wie_b);
   //计算载体坐标系相对导航坐标系的角速度补偿
	matmul("TN", 3, 1, 3, 1, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->EARTH.wnin, 0.0, win_b);
   //计算导航坐标系下的加速度，尚未补偿重力加速度
	matmul("NN", 3, 1, 3, 1, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->SINS.fb_ib,0.0,NAV_Data_Full_p->SINS.fn);
	
	//载体相对导航系的加速度计算
    for(i = 0;i<3;i++)
	{   
	    //计算大地坐标系下的角速度
		NAV_Data_Full_p->SINS.web[i] = NAV_Data_Full_p->SINS.wb_ib[i]- wie_b[i];
		//计算导航系下的角速度
		NAV_Data_Full_p->SINS.wnb[i] = NAV_Data_Full_p->SINS.wb_ib[i]- win_b[i];
		//计算导航系下的加速度，补偿上重力加速度m/s2,举例{-4.0409829786878522e-07, 2.9398186206018674e-07, -9.7879837185144272}
		//
		//NAV_Data_Full_p->SINS.an[i] = NAV_Data_Full_p->SINS.fn[i] + NAV_Data_Full_p->EARTH.gcc[i];
	}
	}
	
	//SINS_UP_ATT(NAV_Data_Full_p,phim);
	
}





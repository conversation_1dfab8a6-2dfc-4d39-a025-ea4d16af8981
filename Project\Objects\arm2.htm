<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\arm2.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\arm2.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Tue Apr 15 10:51:57 2025
<BR><P>
<H3>Maximum Stack Usage =       5664 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; NAV_out &rArr; MagCaliDisplay &rArr; MagInitCali &rArr; MagInitHeading &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[8]">SVC_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[8]">SVC_Handler</a><BR>
 <LI><a href="#[a]">PendSV_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[a]">PendSV_Handler</a><BR>
 <LI><a href="#[22]">CAN0_EWMC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[22]">CAN0_EWMC_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1e]">ADC_IRQHandler</a> from gd32f4xx_it.o(i.ADC_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6]">BusFault_Handler</a> from gd32f4xx_it.o(i.BusFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[22]">CAN0_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[20]">CAN0_RX0_IRQHandler</a> from bsp_can.o(i.CAN0_RX0_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[21]">CAN0_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1f]">CAN0_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4e]">CAN1_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4c]">CAN1_RX0_IRQHandler</a> from bsp_can.o(i.CAN1_RX0_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4d]">CAN1_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4b]">CAN1_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5a]">DCI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[17]">DMA0_Channel0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[18]">DMA0_Channel1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[19]">DMA0_Channel2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1a]">DMA0_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1b]">DMA0_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1c]">DMA0_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1d]">DMA0_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3b]">DMA0_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[44]">DMA1_Channel0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[45]">DMA1_Channel1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[46]">DMA1_Channel2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[47]">DMA1_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[48]">DMA1_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[50]">DMA1_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[51]">DMA1_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[52]">DMA1_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[9]">DebugMon_Handler</a> from gd32f4xx_it.o(i.DebugMon_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[49]">ENET_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4a]">ENET_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3c]">EXMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[12]">EXTI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[34]">EXTI10_15_IRQHandler</a> from gd32f4xx_it.o(i.EXTI10_15_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[13]">EXTI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[14]">EXTI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[15]">EXTI3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[16]">EXTI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[23]">EXTI5_9_IRQHandler</a> from gd32f4xx_it.o(i.EXTI5_9_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[10]">FMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5c]">FPU_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4]">HardFault_Handler</a> from gd32f4xx_it.o(i.HardFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2c]">I2C0_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2b]">I2C0_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2e]">I2C1_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2d]">I2C1_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[55]">I2C2_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[54]">I2C2_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[64]">IPA_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[d]">LVD_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5]">MemManage_Handler</a> from gd32f4xx_it.o(i.MemManage_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3]">NMI_Handler</a> from gd32f4xx_it.o(i.NMI_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[a]">PendSV_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[11]">RCU_CTC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[35]">RTC_Alarm_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[f]">RTC_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2]">Reset_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3d]">SDIO_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2f]">SPI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[30]">SPI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3f]">SPI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5f]">SPI3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[60]">SPI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[61]">SPI5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[8]">SVC_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[b]">SysTick_Handler</a> from gd32f4xx_it.o(i.SysTick_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[66]">SystemInit</a> from system_gd32f4xx.o(i.SystemInit) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[e]">TAMPER_STAMP_IRQHandler</a> from bsp_rtc.o(i.TAMPER_STAMP_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[24]">TIMER0_BRK_TIMER8_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[27]">TIMER0_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[26]">TIMER0_TRG_CMT_TIMER10_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[25]">TIMER0_UP_TIMER9_IRQHandler</a> from gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[28]">TIMER1_IRQHandler</a> from gd32f4xx_it.o(i.TIMER1_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[29]">TIMER2_IRQHandler</a> from gd32f4xx_it.o(i.TIMER2_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2a]">TIMER3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3e]">TIMER4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[42]">TIMER5_DAC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[43]">TIMER6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[37]">TIMER7_BRK_TIMER11_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3a]">TIMER7_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[39]">TIMER7_TRG_CMT_TIMER13_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[38]">TIMER7_UP_TIMER12_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[63]">TLI_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[62]">TLI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5b]">TRNG_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[40]">UART3_IRQHandler</a> from gd32f4xx_it.o(i.UART3_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[41]">UART4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5d]">UART6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5e]">UART7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[31]">USART0_IRQHandler</a> from gd32f4xx_it.o(i.USART0_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[32]">USART1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[33]">USART2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[53]">USART5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4f]">USBFS_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[36]">USBFS_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[57]">USBHS_EP1_In_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[56]">USBHS_EP1_Out_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[59]">USBHS_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[58]">USBHS_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[7]">UsageFault_Handler</a> from gd32f4xx_it.o(i.UsageFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[c]">WWDGT_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[67]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[69]">_sputc</a> from printfa.o(i._sputc) referenced from printfa.o(i.__0sprintf)
 <LI><a href="#[68]">fputc</a> from main.o(i.fputc) referenced from printfa.o(i.__0printf)
 <LI><a href="#[65]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[67]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(.text)
</UL>
<P><STRONG><a name="[16c]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[6a]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[83]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[16d]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[16e]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[16f]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[170]"></a>__rt_lib_shutdown_fini</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry12b.o(.ARM.Collect$$$$0000000E))

<P><STRONG><a name="[171]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[172]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$00000011))

<P><STRONG><a name="[2]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>CAN0_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN0_EWMC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN0_EWMC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>CAN0_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>CAN0_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>CAN1_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>DCI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>DMA0_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA0_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA0_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA0_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA0_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA0_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA0_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>DMA0_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>DMA1_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>ENET_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>ENET_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>EXMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C0_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>I2C0_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>IPA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>LVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>RCU_CTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>SPI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TIMER0_BRK_TIMER8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIMER0_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIMER0_TRG_CMT_TIMER10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>TIMER3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>TIMER4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>TIMER5_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>TIMER6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIMER7_BRK_TIMER11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>TIMER7_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TIMER7_TRG_CMT_TIMER13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIMER7_UP_TIMER12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>TLI_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>TLI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>TRNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>UART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>USART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>USBFS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>USBFS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>USBHS_EP1_In_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>USBHS_EP1_Out_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>USBHS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>USBHS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>WWDGT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[df]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imudemodata
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_out
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart3sendmsg422
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1sendmsg
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_miscel_send
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_navi_and_gnss_send
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_imu_and_gnss_send
</UL>

<P><STRONG><a name="[89]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeSync
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_sector_info_get
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_erase_sector_by_address
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;epoch2time
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN0_RX0_IRQHandler
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matcpy
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Param_Data
</UL>

<P><STRONG><a name="[173]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[6d]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[174]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[175]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[6c]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER0_UP_TIMER9_IRQHandler
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_show_timestamp
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVFileHead
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagCaliDisplay
</UL>

<P><STRONG><a name="[a4]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generateCSVLogFileName
<LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI5_9_IRQHandler
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matinv
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ludcmp
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MahonyUpdate
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_UP
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitCali
</UL>

<P><STRONG><a name="[176]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[6e]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[cc]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg_canout
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVFileHead
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_miscel_send
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagCaliDisplay
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_navi_and_gnss_send
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_imu_and_gnss_send
</UL>

<P><STRONG><a name="[146]"></a>strcpy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, strcpy.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generateCSVLogFileName
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVFileHead
</UL>

<P><STRONG><a name="[6f]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timediff
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeadd
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2epoch
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InnerDot
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qnb2att
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matrixSum
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matmul
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;att2qnb
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qnb2Cnb
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrHeading
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_cali
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MahonyUpdate
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_UP
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LD_Out_Data_Up
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitCali
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitHeading
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_acos
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_imu_and_gnss_send
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floor
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
</UL>

<P><STRONG><a name="[74]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timediff
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeadd
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2str
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cross3
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qnb2att
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matrixSum
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;att2qnb
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qnb2Cnb
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrHeading
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_cali
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MahonyUpdate
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Magnet_Data
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_UP
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Save_mag_angle
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitCali
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitZeroOffsetCompenstation
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitHeading
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_acos
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>

<P><STRONG><a name="[75]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gst2time
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2time
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;epoch2time
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ludcmp
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lubksb
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MahonyUpdate
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_UP
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitZeroOffsetCompenstation
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_acos
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floor
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>

<P><STRONG><a name="[76]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_out
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calcGPRMC_TRA
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseFPGABuff
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ludcmp
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lubksb
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cross3
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InnerDot
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qnb2att
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matrixSum
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matmul
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;att2qnb
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qnb2Cnb
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_cali
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MahonyUpdate
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_UP
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LD_Out_Data_Up
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Save_mag_angle
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitCali
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitZeroOffsetCompenstation
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitHeading
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_acos
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>

<P><STRONG><a name="[77]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2str
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseFPGABuff
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;normv3
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ludcmp
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lubksb
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;att2qnb
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_cali
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Init
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MahonyUpdate
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Magnet_Data
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_UP
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LD_Out_Data_Up
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitCali
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_acos
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_imu_and_gnss_send
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_divzero
</UL>

<P><STRONG><a name="[78]"></a>__aeabi_i2d</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, dflti.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_i2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_out
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calcGPRMC_TRA
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2str
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2epoch
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gst2time
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2time
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;epoch2time
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseFPGABuff
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_cali
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Magnet_Data
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
</UL>

<P><STRONG><a name="[79]"></a>__aeabi_ui2d</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, dfltui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_ui2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timediff
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
</UL>

<P><STRONG><a name="[7a]"></a>__aeabi_d2iz</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, dfixi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_d2iz
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeadd
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gst2time
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2time
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;epoch2time
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Magnet_Data
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>

<P><STRONG><a name="[b9]"></a>__aeabi_f2d</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calcGPRMC_TRA
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseFPGABuff
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MahonyUpdate
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInit
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Param_Data
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_miscel_send
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitHeading
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_navi_and_gnss_send
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_imu_and_gnss_send
</UL>

<P><STRONG><a name="[b5]"></a>__aeabi_cdcmpeq</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, cdcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ludcmp
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lubksb
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matrixSum
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matmul
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Magnet_Data
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitCali
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2
</UL>

<P><STRONG><a name="[a1]"></a>__aeabi_cdcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2str
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gst2time
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2time
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;normv3
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrHeading
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_cali
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MahonyUpdate
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInit
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Magnet_Data
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LD_Out_Data_Up
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
</UL>

<P><STRONG><a name="[87]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2utc
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ludcmp
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrHeading
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_cali
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MahonyUpdate
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInit
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Magnet_Data
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Save_mag_angle
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;install_data_check
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Gyro_data_check
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Acc_data_check
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floor
</UL>

<P><STRONG><a name="[7c]"></a>__aeabi_d2f</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, d2f.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_out
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calcGPRMC_TRA
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseFPGABuff
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LD_Out_Data_Up
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_miscel_send
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitCali
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitHeading
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_imu_and_gnss_send
</UL>

<P><STRONG><a name="[177]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[116]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[7e]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[70]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[178]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[7b]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[179]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[71]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[17a]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[7f]"></a>localtime</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, localtime_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = localtime &rArr; _localtime
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_localtime
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeSync
</UL>

<P><STRONG><a name="[80]"></a>_localtime</STRONG> (Thumb, 134 bytes, Stack size 8 bytes, localtime_i.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _localtime
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;localtime
</UL>

<P><STRONG><a name="[17b]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[7d]"></a>_float_round</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>

<P><STRONG><a name="[17c]"></a>_float_epilogue</STRONG> (Thumb, 92 bytes, Stack size 4 bytes, fepilogue.o(.text), UNUSED)

<P><STRONG><a name="[73]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
</UL>

<P><STRONG><a name="[72]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[112]"></a>__ARM_scalbn</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, dscalb.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __ARM_scalbn
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[17d]"></a>scalbn</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, dscalb.o(.text), UNUSED)

<P><STRONG><a name="[81]"></a>_dsqrt</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, dsqrt.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _dsqrt &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>

<P><STRONG><a name="[82]"></a>__aeabi_d2ulz</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[6b]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[17e]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[17f]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[180]"></a>__decompress1</STRONG> (Thumb, 86 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[1e]"></a>ADC_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.ADC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_interrupt_flag_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[85]"></a>Acc_data_check</STRONG> (Thumb, 146 bytes, Stack size 24 bytes, nav_imu.o(i.Acc_data_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Acc_data_check &rArr; __hardfp_fabs
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
</UL>

<P><STRONG><a name="[6]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>CAN0_RX0_IRQHandler</STRONG> (Thumb, 874 bytes, Stack size 8 bytes, bsp_can.o(i.CAN0_RX0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = CAN0_RX0_IRQHandler &rArr; can_message_receive
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_message_receive
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_SystemReset
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 740 bytes, Stack size 8 bytes, bsp_can.o(i.CAN1_RX0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = CAN1_RX0_IRQHandler &rArr; DRam_Write
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_message_receive
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRam_Write
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_SystemReset
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[8e]"></a>CH378ByteLocate</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, file_sys.o(i.CH378ByteLocate))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = CH378ByteLocate &rArr; Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wait378Interrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
</UL>

<P><STRONG><a name="[92]"></a>CH378ByteWrite</STRONG> (Thumb, 74 bytes, Stack size 24 bytes, file_sys.o(i.CH378ByteWrite))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = CH378ByteWrite &rArr; Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wait378Interrupt
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378WriteOfsBlock
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVFileHead
</UL>

<P><STRONG><a name="[94]"></a>CH378FileCreate</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, file_sys.o(i.CH378FileCreate))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = CH378FileCreate &rArr; CH378SendCmdWaitInt &rArr; Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SetFileName
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SendCmdWaitInt
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
</UL>

<P><STRONG><a name="[97]"></a>CH378FileOpen</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, file_sys.o(i.CH378FileOpen))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = CH378FileOpen &rArr; CH378SendCmdWaitInt &rArr; Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SetFileName
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SendCmdWaitInt
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
</UL>

<P><STRONG><a name="[98]"></a>CH378GetDiskStatus</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, file_sys.o(i.CH378GetDiskStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = CH378GetDiskStatus &rArr; CH378ReadVar8 &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ReadVar8
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
</UL>

<P><STRONG><a name="[9a]"></a>CH378GetIntStatus</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, file_sys.o(i.CH378GetIntStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xReadCH378Data
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wait378Interrupt
</UL>

<P><STRONG><a name="[99]"></a>CH378ReadVar8</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, file_sys.o(i.CH378ReadVar8))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = CH378ReadVar8 &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xReadCH378Data
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetDiskStatus
</UL>

<P><STRONG><a name="[96]"></a>CH378SendCmdWaitInt</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, file_sys.o(i.CH378SendCmdWaitInt))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = CH378SendCmdWaitInt &rArr; Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wait378Interrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378FileOpen
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378FileCreate
</UL>

<P><STRONG><a name="[95]"></a>CH378SetFileName</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, file_sys.o(i.CH378SetFileName))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = CH378SetFileName &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378FileOpen
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378FileCreate
</UL>

<P><STRONG><a name="[93]"></a>CH378WriteOfsBlock</STRONG> (Thumb, 84 bytes, Stack size 24 bytes, file_sys.o(i.CH378WriteOfsBlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = CH378WriteOfsBlock &rArr; CH378_mDelayuS &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_mDelayuS
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteWrite
</UL>

<P><STRONG><a name="[9d]"></a>CH378_mDelaymS</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, ch378_hal.o(i.CH378_mDelaymS))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CH378_mDelaymS
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
</UL>

<P><STRONG><a name="[9c]"></a>CH378_mDelayuS</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, ch378_hal.o(i.CH378_mDelayuS))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = CH378_mDelayuS &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wait378Interrupt
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378WriteOfsBlock
</UL>

<P><STRONG><a name="[a0]"></a>CorrHeading</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, nav_kf.o(i.CorrHeading))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = CorrHeading &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
</UL>

<P><STRONG><a name="[a5]"></a>DRam_Read</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, bsp_fmc.o(i.DRam_Read))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DRam_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI5_9_IRQHandler
</UL>

<P><STRONG><a name="[8d]"></a>DRam_Write</STRONG> (Thumb, 48 bytes, Stack size 12 bytes, bsp_fmc.o(i.DRam_Write))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = DRam_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_RX0_IRQHandler
</UL>

<P><STRONG><a name="[9]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>EXTI10_15_IRQHandler</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.EXTI10_15_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI10_15_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_get
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>EXTI5_9_IRQHandler</STRONG> (Thumb, 122 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.EXTI5_9_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 992<LI>Call Chain = EXTI5_9_IRQHandler &rArr; synthesisLogBuf &rArr; time2str &rArr; __hardfp_pow &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_get
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_clear
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRam_Read
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[e3]"></a>Earth_Init</STRONG> (Thumb, 228 bytes, Stack size 0 bytes, nav_sins.o(i.Earth_Init))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Init
</UL>

<P><STRONG><a name="[a8]"></a>Earth_UP</STRONG> (Thumb, 1402 bytes, Stack size 192 bytes, nav_sins.o(i.Earth_UP))
<BR><BR>[Stack]<UL><LI>Max Depth = 432<LI>Call Chain = Earth_UP &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cross3
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matrixSum
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function_LD_TEST
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Init
</UL>

<P><STRONG><a name="[ae]"></a>FOG6001RecData</STRONG> (Thumb, 164 bytes, Stack size 8 bytes, setparabao.o(i.FOG6001RecData))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = FOG6001RecData &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analysisRxdata
</UL>

<P><STRONG><a name="[b0]"></a>Get_IMU_Data</STRONG> (Thumb, 1830 bytes, Stack size 56 bytes, nav_imu.o(i.Get_IMU_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = Get_IMU_Data &rArr; InitZeroOffsetCompenstation &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetNavStatus
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitZeroOffsetCompenstation
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Gyro_data_check
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Acc_data_check
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function_LD_TEST
</UL>

<P><STRONG><a name="[b4]"></a>Get_Magnet_Data</STRONG> (Thumb, 1720 bytes, Stack size 56 bytes, nav_magnet.o(i.Get_Magnet_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = Get_Magnet_Data &rArr; Save_mag_angle &rArr; __hardfp_atan2 &rArr; atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmpeq
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;norm
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Save_mag_angle
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function_LD_TEST
</UL>

<P><STRONG><a name="[b8]"></a>Get_Param_Data</STRONG> (Thumb, 342 bytes, Stack size 16 bytes, nav_imu.o(i.Get_Param_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = Get_Param_Data &rArr; install_data_check &rArr; norm &rArr; InnerDot &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;install_data_check
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function_LD_TEST
</UL>

<P><STRONG><a name="[b2]"></a>Gyro_data_check</STRONG> (Thumb, 146 bytes, Stack size 24 bytes, nav_imu.o(i.Gyro_data_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Gyro_data_check &rArr; __hardfp_fabs
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
</UL>

<P><STRONG><a name="[4]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.HardFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[bb]"></a>INS_Init</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, main.o(i.INS_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = INS_Init &rArr; gd_eval_com_init &rArr; usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_enable
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_init
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_gpio_init
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b1]"></a>InitZeroOffsetCompenstation</STRONG> (Thumb, 464 bytes, Stack size 16 bytes, nav_imu.o(i.InitZeroOffsetCompenstation))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = InitZeroOffsetCompenstation &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
</UL>

<P><STRONG><a name="[c2]"></a>InnerDot</STRONG> (Thumb, 106 bytes, Stack size 32 bytes, nav_math.o(i.InnerDot))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = InnerDot &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;norm
</UL>

<P><STRONG><a name="[c3]"></a>LD_Out_Data_Up</STRONG> (Thumb, 1226 bytes, Stack size 40 bytes, nav.o(i.LD_Out_Data_Up))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = LD_Out_Data_Up &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function_LD_TEST
</UL>

<P><STRONG><a name="[c4]"></a>LEDIndicator</STRONG> (Thumb, 98 bytes, Stack size 8 bytes, main.o(i.LEDIndicator))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LEDIndicator
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c5]"></a>Lever</STRONG> (Thumb, 112 bytes, Stack size 208 bytes, nav_sins.o(i.Lever))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = Lever &rArr; matmul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matrixSum
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matmul
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qnb2Cnb
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Init
</UL>

<P><STRONG><a name="[c8]"></a>Load_Standard_Data</STRONG> (Thumb, 148 bytes, Stack size 16 bytes, nav_imu.o(i.Load_Standard_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Load_Standard_Data
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetNavStandardFlag
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function_LD_TEST
</UL>

<P><STRONG><a name="[ca]"></a>MagCaliDisplay</STRONG> (Thumb, 168 bytes, Stack size 48 bytes, lpbus.o(i.MagCaliDisplay))
<BR><BR>[Stack]<UL><LI>Max Depth = 5632<LI>Call Chain = MagCaliDisplay &rArr; MagInitCali &rArr; MagInitHeading &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart3sendmsg422
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitCali
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_out
</UL>

<P><STRONG><a name="[cf]"></a>MagInit</STRONG> (Thumb, 452 bytes, Stack size 8 bytes, nav_magnet.o(i.MagInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = MagInit &rArr; comm_set_customPara &rArr; fmc_write_8bit_data &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_set_customPara
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function_LD_TEST
</UL>

<P><STRONG><a name="[ce]"></a>MagInitCali</STRONG> (Thumb, 1324 bytes, Stack size 5248 bytes, nav_magnet.o(i.MagInitCali))
<BR><BR>[Stack]<UL><LI>Max Depth = 5584<LI>Call Chain = MagInitCali &rArr; MagInitHeading &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmpeq
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matinv
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matmul
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_set_customPara
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitHeading
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagCaliDisplay
</UL>

<P><STRONG><a name="[d2]"></a>MagInitHeading</STRONG> (Thumb, 494 bytes, Stack size 96 bytes, nav_magnet.o(i.MagInitHeading))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = MagInitHeading &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitCali
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
</UL>

<P><STRONG><a name="[d4]"></a>MahonyUpdate</STRONG> (Thumb, 8624 bytes, Stack size 576 bytes, nav_mahony.o(i.MahonyUpdate))
<BR><BR>[Stack]<UL><LI>Max Depth = 1000<LI>Call Chain = MahonyUpdate &rArr; qnb2att &rArr; __hardfp_atan2 &rArr; atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;normv3
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cross3
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qnb2att
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;norm
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matrixSum
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matmul
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qnb2Cnb
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_acos
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function_LD_TEST
</UL>

<P><STRONG><a name="[d8]"></a>Mat_Tr</STRONG> (Thumb, 52 bytes, Stack size 12 bytes, nav_math.o(i.Mat_Tr))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = Mat_Tr
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MahonyUpdate
</UL>

<P><STRONG><a name="[5]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[d9]"></a>NAV_function_LD_TEST</STRONG> (Thumb, 198 bytes, Stack size 8 bytes, nav_app.o(i.NAV_function_LD_TEST))
<BR><BR>[Stack]<UL><LI>Max Depth = 1008<LI>Call Chain = NAV_function_LD_TEST &rArr; MahonyUpdate &rArr; qnb2att &rArr; __hardfp_atan2 &rArr; atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_cali
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Init
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MahonyUpdate
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInit
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Load_Standard_Data
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Param_Data
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Magnet_Data
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_UP
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Param_Data_Init
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetNavStatus
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LD_Out_Data_Up
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[dd]"></a>NAV_out</STRONG> (Thumb, 400 bytes, Stack size 32 bytes, lpbus.o(i.NAV_out))
<BR><BR>[Stack]<UL><LI>Max Depth = 5664<LI>Call Chain = NAV_out &rArr; MagCaliDisplay &rArr; MagInitCali &rArr; MagInitHeading &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1sendmsg
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagCaliDisplay
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[3]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[da]"></a>Param_Data_Init</STRONG> (Thumb, 166 bytes, Stack size 0 bytes, nav_app.o(i.Param_Data_Init))
<BR><BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function_LD_TEST
</UL>

<P><STRONG><a name="[c6]"></a>Qnb2Cnb</STRONG> (Thumb, 844 bytes, Stack size 112 bytes, nav_math.o(i.Qnb2Cnb))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = Qnb2Cnb &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MahonyUpdate
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lever
</UL>

<P><STRONG><a name="[e1]"></a>Query378Interrupt</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ch378_spi_hw.o(i.Query378Interrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Query378Interrupt
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wait378Interrupt
</UL>

<P><STRONG><a name="[db]"></a>SINS_Init</STRONG> (Thumb, 442 bytes, Stack size 24 bytes, nav_sins.o(i.SINS_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 504<LI>Call Chain = SINS_Init &rArr; StartCoarseAlign &rArr; att2qnb &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_UP
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lever
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function_LD_TEST
</UL>

<P><STRONG><a name="[e5]"></a>SPI_Exchange</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, ch378_spi_hw.o(i.SPI_Exchange))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI_Exchange
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_transmit
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_receive
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xReadCH378Data
</UL>

<P><STRONG><a name="[b6]"></a>Save_mag_angle</STRONG> (Thumb, 224 bytes, Stack size 32 bytes, nav_magnet.o(i.Save_mag_angle))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = Save_mag_angle &rArr; __hardfp_atan2 &rArr; atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Magnet_Data
</UL>

<P><STRONG><a name="[c9]"></a>SetNavStandardFlag</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, nav.o(i.SetNavStandardFlag))
<BR><BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Load_Standard_Data
</UL>

<P><STRONG><a name="[b3]"></a>SetNavStatus</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, nav.o(i.SetNavStatus))
<BR><BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function_LD_TEST
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
</UL>

<P><STRONG><a name="[e4]"></a>StartCoarseAlign</STRONG> (Thumb, 3028 bytes, Stack size 128 bytes, nav_sins.o(i.StartCoarseAlign))
<BR><BR>[Stack]<UL><LI>Max Depth = 480<LI>Call Chain = StartCoarseAlign &rArr; att2qnb &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;norm
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;att2qnb
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qnb2Cnb
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrHeading
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitHeading
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_acos
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Init
</UL>

<P><STRONG><a name="[b]"></a>SysTick_Handler</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysTick_Handler
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_decrement
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[66]"></a>SystemInit</STRONG> (Thumb, 184 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SystemInit &rArr; system_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(.text)
</UL>
<P><STRONG><a name="[e]"></a>TAMPER_STAMP_IRQHandler</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, bsp_rtc.o(i.TAMPER_STAMP_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = TAMPER_STAMP_IRQHandler &rArr; rtc_show_timestamp &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_flag_get
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_flag_clear
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_show_timestamp
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIMER0_UP_TIMER9_IRQHandler</STRONG> (Thumb, 122 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 944<LI>Call Chain = TIMER0_UP_TIMER9_IRQHandler &rArr; generateCSVLogFileName &rArr; sow2Date &rArr; gst2time &rArr; epoch2time &rArr; __hardfp_floor &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_get
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeSync
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generateCSVLogFileName
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIMER1_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.TIMER1_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIMER2_IRQHandler</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.TIMER2_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>UART3_IRQHandler</STRONG> (Thumb, 130 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.UART3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = UART3_IRQHandler &rArr; usart_interrupt_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_get
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_disable
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>USART0_IRQHandler</STRONG> (Thumb, 130 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.USART0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USART0_IRQHandler &rArr; usart_interrupt_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_get
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_disable
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[f7]"></a>Uart_SendMsg</STRONG> (Thumb, 52 bytes, Stack size 24 bytes, bsp_fmc.o(i.Uart_SendMsg))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_miscel_send
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_navi_and_gnss_send
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_imu_and_gnss_send
</UL>

<P><STRONG><a name="[7]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[91]"></a>Wait378Interrupt</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, file_sys.o(i.Wait378Interrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_mDelayuS
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Query378Interrupt
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetIntStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SendCmdWaitInt
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteWrite
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteLocate
</UL>

<P><STRONG><a name="[f8]"></a>__0printf</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[181]"></a>__1printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[16b]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
</UL>

<P><STRONG><a name="[182]"></a>__c89printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[183]"></a>printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[fa]"></a>__0sprintf</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[184]"></a>__1sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[cb]"></a>__2sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg_canout
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generateCSVLogFileName
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2str
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sow2Date
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_show_timestamp
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_miscel_send
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagCaliDisplay
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_navi_and_gnss_send
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_imu_and_gnss_send
</UL>

<P><STRONG><a name="[185]"></a>__c89sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[186]"></a>sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[101]"></a>__ARM_fpclassify</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, fpclassify.o(i.__ARM_fpclassify))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __ARM_fpclassify
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
</UL>

<P><STRONG><a name="[107]"></a>__ARM_fpclassifyf</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, fpclassifyf.o(i.__ARM_fpclassifyf))
<BR><BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>

<P><STRONG><a name="[d6]"></a>__hardfp_acos</STRONG> (Thumb, 738 bytes, Stack size 72 bytes, acos.o(i.__hardfp_acos))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = __hardfp_acos &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MahonyUpdate
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
</UL>

<P><STRONG><a name="[100]"></a>__hardfp_asin</STRONG> (Thumb, 770 bytes, Stack size 88 bytes, asin.o(i.__hardfp_asin))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = __hardfp_asin &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qnb2att
</UL>

<P><STRONG><a name="[104]"></a>__hardfp_atan</STRONG> (Thumb, 622 bytes, Stack size 48 bytes, atan.o(i.__hardfp_atan))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
</UL>

<P><STRONG><a name="[e9]"></a>__hardfp_atan2</STRONG> (Thumb, 448 bytes, Stack size 56 bytes, atan2.o(i.__hardfp_atan2))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = __hardfp_atan2 &rArr; atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmpeq
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qnb2att
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Save_mag_angle
</UL>

<P><STRONG><a name="[d3]"></a>__hardfp_atan2f</STRONG> (Thumb, 594 bytes, Stack size 32 bytes, atan2f.o(i.__hardfp_atan2f))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __hardfp_atan2f
</UL>
<BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_underflow
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan2
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassifyf
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitHeading
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
</UL>

<P><STRONG><a name="[aa]"></a>__hardfp_cos</STRONG> (Thumb, 180 bytes, Stack size 32 bytes, cos.o(i.__hardfp_cos))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;att2qnb
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MahonyUpdate
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_UP
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitHeading
</UL>

<P><STRONG><a name="[10d]"></a>__hardfp_exp</STRONG> (Thumb, 714 bytes, Stack size 72 bytes, exp.o(i.__hardfp_exp))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = __hardfp_exp &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseFPGABuff
</UL>

<P><STRONG><a name="[86]"></a>__hardfp_fabs</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, fabs.o(i.__hardfp_fabs))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ludcmp
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_cali
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MahonyUpdate
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Magnet_Data
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LD_Out_Data_Up
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Gyro_data_check
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Acc_data_check
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
</UL>

<P><STRONG><a name="[10f]"></a>__hardfp_floor</STRONG> (Thumb, 252 bytes, Stack size 40 bytes, floor.o(i.__hardfp_floor))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = __hardfp_floor &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeadd
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;epoch2time
</UL>

<P><STRONG><a name="[110]"></a>__hardfp_pow</STRONG> (Thumb, 3072 bytes, Stack size 192 bytes, pow.o(i.__hardfp_pow))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = __hardfp_pow &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_divzero
</UL>
<BR>[Called By]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2str
</UL>

<P><STRONG><a name="[a9]"></a>__hardfp_sin</STRONG> (Thumb, 180 bytes, Stack size 32 bytes, sin.o(i.__hardfp_sin))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;att2qnb
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MahonyUpdate
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_UP
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitHeading
</UL>

<P><STRONG><a name="[ab]"></a>__hardfp_sqrt</STRONG> (Thumb, 122 bytes, Stack size 32 bytes, sqrt.o(i.__hardfp_sqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = __hardfp_sqrt &rArr; _dsqrt &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;norm
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MahonyUpdate
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_UP
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitCali
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
</UL>

<P><STRONG><a name="[10a]"></a>__ieee754_rem_pio2</STRONG> (Thumb, 938 bytes, Stack size 120 bytes, rred.o(i.__ieee754_rem_pio2))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = __ieee754_rem_pio2 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>

<P><STRONG><a name="[10c]"></a>__kernel_cos</STRONG> (Thumb, 322 bytes, Stack size 64 bytes, cos_i.o(i.__kernel_cos))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = __kernel_cos &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>

<P><STRONG><a name="[fe]"></a>__kernel_poly</STRONG> (Thumb, 248 bytes, Stack size 24 bytes, poly.o(i.__kernel_poly))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_acos
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>

<P><STRONG><a name="[10b]"></a>__kernel_sin</STRONG> (Thumb, 280 bytes, Stack size 72 bytes, sin_i.o(i.__kernel_sin))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = __kernel_sin &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>

<P><STRONG><a name="[111]"></a>__mathlib_dbl_divzero</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_divzero))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = __mathlib_dbl_divzero &rArr; __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[fb]"></a>__mathlib_dbl_infnan</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_infnan))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __mathlib_dbl_infnan &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_acos
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>

<P><STRONG><a name="[105]"></a>__mathlib_dbl_infnan2</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_infnan2))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __mathlib_dbl_infnan2 &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[fd]"></a>__mathlib_dbl_invalid</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_invalid))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = __mathlib_dbl_invalid &rArr; __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_acos
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[10e]"></a>__mathlib_dbl_overflow</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_overflow))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __mathlib_dbl_overflow &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[102]"></a>__mathlib_dbl_underflow</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_underflow))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __mathlib_dbl_underflow &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
</UL>

<P><STRONG><a name="[109]"></a>__mathlib_flt_infnan2</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_infnan2))
<BR><BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>

<P><STRONG><a name="[108]"></a>__mathlib_flt_underflow</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_underflow))
<BR><BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>

<P><STRONG><a name="[187]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[188]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[189]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[fc]"></a>__set_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__set_errno))
<BR><BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_acos
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[84]"></a>adc_interrupt_flag_clear</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_interrupt_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>

<P><STRONG><a name="[117]"></a>analysisRxdata</STRONG> (Thumb, 290 bytes, Stack size 16 bytes, protocol.o(i.analysisRxdata))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = analysisRxdata &rArr; FOG6001RecData &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FOG6001RecData
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[106]"></a>atan</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, atan.o(i.atan))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2
</UL>

<P><STRONG><a name="[ea]"></a>att2qnb</STRONG> (Thumb, 694 bytes, Stack size 112 bytes, nav_math.o(i.att2qnb))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = att2qnb &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
</UL>

<P><STRONG><a name="[bd]"></a>bsp_gpio_init</STRONG> (Thumb, 404 bytes, Stack size 8 bytes, bsp_gpio.o(i.bsp_gpio_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = bsp_gpio_init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[be]"></a>bsp_tim_init</STRONG> (Thumb, 330 bytes, Stack size 32 bytes, bsp_tim.o(i.bsp_tim_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = bsp_tim_init &rArr; timer_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_timer_clock_prescaler_config
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_enable
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_init
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_enable
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_shadow_config
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_pulse_value_config
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_mode_config
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_config
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_auto_reload_shadow_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[125]"></a>calcGPRMC_TRA</STRONG> (Thumb, 210 bytes, Stack size 48 bytes, main.o(i.calcGPRMC_TRA))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = calcGPRMC_TRA &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
</UL>

<P><STRONG><a name="[88]"></a>can_message_receive</STRONG> (Thumb, 228 bytes, Stack size 8 bytes, gd32f4xx_can.o(i.can_message_receive))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = can_message_receive
</UL>
<BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_RX0_IRQHandler
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN0_RX0_IRQHandler
</UL>

<P><STRONG><a name="[13a]"></a>comm_axis_read</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, computerframeparse.o(i.comm_axis_read))
<BR><BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_imu_and_gnss_send
</UL>

<P><STRONG><a name="[136]"></a>comm_read_currentFreq</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, computerframeparse.o(i.comm_read_currentFreq))
<BR><BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_form
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_miscel_send
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_navi_and_gnss_send
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_imu_and_gnss_send
</UL>

<P><STRONG><a name="[d0]"></a>comm_set_customPara</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, computerframeparse.o(i.comm_set_customPara))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = comm_set_customPara &rArr; fmc_write_8bit_data &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_erase_sector_by_address
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hash32
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInit
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitCali
</UL>

<P><STRONG><a name="[af]"></a>crc_verify_8bit</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, firmwareupdatefile.o(i.crc_verify_8bit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = crc_verify_8bit
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FOG6001RecData
</UL>

<P><STRONG><a name="[ad]"></a>cross3</STRONG> (Thumb, 294 bytes, Stack size 32 bytes, nav_math.o(i.cross3))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = cross3 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MahonyUpdate
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_UP
</UL>

<P><STRONG><a name="[eb]"></a>delay_decrement</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, systick.o(i.delay_decrement))
<BR><BR>[Called By]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[bc]"></a>delay_init</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, systick.o(i.delay_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = delay_init
</UL>
<BR>[Calls]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_clksource_set
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[9e]"></a>delay_ms</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, systick.o(i.delay_ms))
<BR><BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mDelaymS
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_mDelaymS
</UL>

<P><STRONG><a name="[9f]"></a>delay_us</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, systick.o(i.delay_us))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_mDelayuS
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
</UL>

<P><STRONG><a name="[12a]"></a>epoch2time</STRONG> (Thumb, 354 bytes, Stack size 112 bytes, time_unify.o(i.epoch2time))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = epoch2time &rArr; __hardfp_floor &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floor
</UL>
<BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gst2time
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2utc
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2time
</UL>

<P><STRONG><a name="[a3]"></a>exti_interrupt_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_exti.o(i.exti_interrupt_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI5_9_IRQHandler
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI10_15_IRQHandler
</UL>

<P><STRONG><a name="[a2]"></a>exti_interrupt_flag_get</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_exti.o(i.exti_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI5_9_IRQHandler
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI10_15_IRQHandler
</UL>

<P><STRONG><a name="[103]"></a>fabs</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, fabs.o(i.fabs))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
</UL>

<P><STRONG><a name="[12b]"></a>fmc_byte_program</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, gd32f4xx_fmc.o(i.fmc_byte_program))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = fmc_byte_program &rArr; fmc_ready_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_ready_wait
</UL>
<BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
</UL>

<P><STRONG><a name="[126]"></a>fmc_erase_sector_by_address</STRONG> (Thumb, 68 bytes, Stack size 48 bytes, bsp_fmc.o(i.fmc_erase_sector_by_address))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = fmc_erase_sector_by_address &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_unlock
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_sector_erase
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_lock
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_flag_clear
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_sector_info_get
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_set_customPara
</UL>

<P><STRONG><a name="[12f]"></a>fmc_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_fmc.o(i.fmc_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_erase_sector_by_address
</UL>

<P><STRONG><a name="[131]"></a>fmc_lock</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_fmc.o(i.fmc_lock))
<BR><BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_erase_sector_by_address
</UL>

<P><STRONG><a name="[12c]"></a>fmc_ready_wait</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, gd32f4xx_fmc.o(i.fmc_ready_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = fmc_ready_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_state_get
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_sector_erase
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_byte_program
</UL>

<P><STRONG><a name="[130]"></a>fmc_sector_erase</STRONG> (Thumb, 90 bytes, Stack size 12 bytes, gd32f4xx_fmc.o(i.fmc_sector_erase))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = fmc_sector_erase &rArr; fmc_ready_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_ready_wait
</UL>
<BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_erase_sector_by_address
</UL>

<P><STRONG><a name="[12d]"></a>fmc_sector_info_get</STRONG> (Thumb, 366 bytes, Stack size 40 bytes, bsp_fmc.o(i.fmc_sector_info_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_erase_sector_by_address
</UL>

<P><STRONG><a name="[132]"></a>fmc_state_get</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, gd32f4xx_fmc.o(i.fmc_state_get))
<BR><BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_ready_wait
</UL>

<P><STRONG><a name="[12e]"></a>fmc_unlock</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_fmc.o(i.fmc_unlock))
<BR><BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_erase_sector_by_address
</UL>

<P><STRONG><a name="[128]"></a>fmc_write_8bit_data</STRONG> (Thumb, 132 bytes, Stack size 88 bytes, bsp_fmc.o(i.fmc_write_8bit_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = fmc_write_8bit_data &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_unlock
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_sector_erase
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_lock
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_flag_clear
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_byte_program
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_sector_info_get
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sector_name_to_number
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_set_customPara
</UL>

<P><STRONG><a name="[68]"></a>fputc</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, main.o(i.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = fputc &rArr; usart_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0printf)
</UL>
<P><STRONG><a name="[135]"></a>frame_form</STRONG> (Thumb, 104 bytes, Stack size 8 bytes, frame_analysis.o(i.frame_form))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = frame_form &rArr; frame_miscel_send &rArr; Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_miscel_send
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_read_currentFreq
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_navi_and_gnss_send
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_imu_and_gnss_send
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
</UL>

<P><STRONG><a name="[138]"></a>frame_imu_and_gnss_send</STRONG> (Thumb, 1218 bytes, Stack size 280 bytes, frame_analysis.o(i.frame_imu_and_gnss_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 368<LI>Call Chain = frame_imu_and_gnss_send &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_SendMsg
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_read_currentFreq
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_axis_read
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_form
</UL>

<P><STRONG><a name="[139]"></a>frame_miscel_send</STRONG> (Thumb, 1234 bytes, Stack size 560 bytes, frame_analysis.o(i.frame_miscel_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = frame_miscel_send &rArr; Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_SendMsg
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_read_currentFreq
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_form
</UL>

<P><STRONG><a name="[137]"></a>frame_navi_and_gnss_send</STRONG> (Thumb, 460 bytes, Stack size 256 bytes, frame_analysis.o(i.frame_navi_and_gnss_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = frame_navi_and_gnss_send &rArr; Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_update
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_SendMsg
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_read_currentFreq
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_form
</UL>

<P><STRONG><a name="[c0]"></a>gd_eval_com_init</STRONG> (Thumb, 284 bytes, Stack size 16 bytes, main.o(i.gd_eval_com_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 132<LI>Call Chain = gd_eval_com_init &rArr; usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_word_length_set
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_transmit_config
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_stop_bit_set
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_receive_config
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_parity_config
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_enable
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[f1]"></a>generateCSVLogFileName</STRONG> (Thumb, 86 bytes, Stack size 544 bytes, logger.o(i.generateCSVLogFileName))
<BR><BR>[Stack]<UL><LI>Max Depth = 936<LI>Call Chain = generateCSVLogFileName &rArr; sow2Date &rArr; gst2time &rArr; epoch2time &rArr; __hardfp_floor &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sow2Date
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER0_UP_TIMER9_IRQHandler
</UL>

<P><STRONG><a name="[149]"></a>get_16bit_D32</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, main.o(i.get_16bit_D32))
<BR><BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
</UL>

<P><STRONG><a name="[14a]"></a>get_16bit_D64</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, main.o(i.get_16bit_D64))
<BR><BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
</UL>

<P><STRONG><a name="[148]"></a>get_16bit_Int32</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, main.o(i.get_16bit_Int32))
<BR><BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
</UL>

<P><STRONG><a name="[147]"></a>get_fpgadata</STRONG> (Thumb, 2326 bytes, Stack size 1072 bytes, main.o(i.get_fpgadata))
<BR><BR>[Stack]<UL><LI>Max Depth = 1688<LI>Call Chain = get_fpgadata &rArr; frame_form &rArr; frame_miscel_send &rArr; Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_form
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_16bit_Int32
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_16bit_D64
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_16bit_D32
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calcGPRMC_TRA
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[13c]"></a>gpio_af_set</STRONG> (Thumb, 94 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_af_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[8a]"></a>gpio_bit_reset</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_reset))
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LEDIndicator
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_RX0_IRQHandler
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN0_RX0_IRQHandler
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetIntStatus
</UL>

<P><STRONG><a name="[8b]"></a>gpio_bit_set</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_set))
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LEDIndicator
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_RX0_IRQHandler
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN0_RX0_IRQHandler
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378WriteOfsBlock
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SetFileName
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SendCmdWaitInt
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ReadVar8
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetIntStatus
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteWrite
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteLocate
</UL>

<P><STRONG><a name="[158]"></a>gpio_bit_toggle</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_toggle))
<BR><BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[157]"></a>gpio_bit_write</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_write))
<BR><BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e2]"></a>gpio_input_bit_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_input_bit_get))
<BR><BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Query378Interrupt
</UL>

<P><STRONG><a name="[119]"></a>gpio_mode_set</STRONG> (Thumb, 78 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_mode_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_gpio_init
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[11a]"></a>gpio_output_options_set</STRONG> (Thumb, 66 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_output_options_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_output_options_set
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_gpio_init
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[14b]"></a>gpst2time</STRONG> (Thumb, 142 bytes, Stack size 48 bytes, time_unify.o(i.gpst2time))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = gpst2time &rArr; epoch2time &rArr; __hardfp_floor &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;epoch2time
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeSync
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
</UL>

<P><STRONG><a name="[14c]"></a>gpst2utc</STRONG> (Thumb, 164 bytes, Stack size 88 bytes, time_unify.o(i.gpst2utc))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = gpst2utc &rArr; epoch2time &rArr; __hardfp_floor &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timediff
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeadd
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;epoch2time
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeSync
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
</UL>

<P><STRONG><a name="[14f]"></a>gst2time</STRONG> (Thumb, 142 bytes, Stack size 48 bytes, time_unify.o(i.gst2time))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = gst2time &rArr; epoch2time &rArr; __hardfp_floor &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;epoch2time
</UL>
<BR>[Called By]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sow2Date
</UL>

<P><STRONG><a name="[dc]"></a>gyro_cali</STRONG> (Thumb, 608 bytes, Stack size 48 bytes, nav_sins.o(i.gyro_cali))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = gyro_cali &rArr; norm &rArr; InnerDot &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;norm
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function_LD_TEST
</UL>

<P><STRONG><a name="[127]"></a>hash32</STRONG> (Thumb, 60 bytes, Stack size 20 bytes, computerframeparse.o(i.hash32))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = hash32
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_set_customPara
</UL>

<P><STRONG><a name="[150]"></a>imudemodata</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, lpbus.o(i.imudemodata))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = imudemodata
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ba]"></a>install_data_check</STRONG> (Thumb, 56 bytes, Stack size 24 bytes, nav_imu.o(i.install_data_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = install_data_check &rArr; norm &rArr; InnerDot &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;norm
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Param_Data
</UL>

<P><STRONG><a name="[151]"></a>lubksb</STRONG> (Thumb, 322 bytes, Stack size 56 bytes, nav_math.o(i.lubksb))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = lubksb &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmpeq
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matinv
</UL>

<P><STRONG><a name="[152]"></a>ludcmp</STRONG> (Thumb, 820 bytes, Stack size 80 bytes, nav_math.o(i.ludcmp))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = ludcmp &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmpeq
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matinv
</UL>

<P><STRONG><a name="[153]"></a>mDelaymS</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, ch395spi.o(i.mDelaymS))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = mDelaymS
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[65]"></a>main</STRONG> (Thumb, 200 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 5664<LI>Call Chain = main &rArr; NAV_out &rArr; MagCaliDisplay &rArr; MagInitCali &rArr; MagInitHeading &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_vector_table_set
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_write
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_toggle
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_flag_get
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_all_reset_flag_clear
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg_canout
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mDelaymS
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imudemodata
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analysisRxdata
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_out
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function_LD_TEST
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LEDIndicator
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[15a]"></a>matcpy</STRONG> (Thumb, 30 bytes, Stack size 24 bytes, nav_math.o(i.matcpy))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = matcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matinv
</UL>

<P><STRONG><a name="[d1]"></a>matinv</STRONG> (Thumb, 172 bytes, Stack size 32 bytes, nav_math.o(i.matinv))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = matinv &rArr; ludcmp &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matcpy
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ludcmp
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lubksb
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitCali
</UL>

<P><STRONG><a name="[c7]"></a>matmul</STRONG> (Thumb, 928 bytes, Stack size 120 bytes, nav_math.o(i.matmul))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = matmul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmpeq
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MahonyUpdate
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitCali
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lever
</UL>

<P><STRONG><a name="[ac]"></a>matrixSum</STRONG> (Thumb, 300 bytes, Stack size 48 bytes, nav_math.o(i.matrixSum))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = matrixSum &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmpeq
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MahonyUpdate
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_UP
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lever
</UL>

<P><STRONG><a name="[b7]"></a>norm</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, nav_math.o(i.norm))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = norm &rArr; InnerDot &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InnerDot
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;normv3
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_cali
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MahonyUpdate
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Magnet_Data
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;install_data_check
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
</UL>

<P><STRONG><a name="[d5]"></a>normv3</STRONG> (Thumb, 148 bytes, Stack size 32 bytes, nav_math.o(i.normv3))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = normv3 &rArr; norm &rArr; InnerDot &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;norm
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MahonyUpdate
</UL>

<P><STRONG><a name="[bf]"></a>nvic_irq_enable</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, gd32f4xx_misc.o(i.nvic_irq_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[15b]"></a>nvic_priority_group_set</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_misc.o(i.nvic_priority_group_set))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
</UL>

<P><STRONG><a name="[154]"></a>nvic_vector_table_set</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_misc.o(i.nvic_vector_table_set))
<BR><BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[15c]"></a>parseFPGABuff</STRONG> (Thumb, 1526 bytes, Stack size 64 bytes, logger.o(i.parseFPGABuff))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = parseFPGABuff &rArr; __hardfp_exp &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
</UL>

<P><STRONG><a name="[d7]"></a>qnb2att</STRONG> (Thumb, 1180 bytes, Stack size 200 bytes, nav_math.o(i.qnb2att))
<BR><BR>[Stack]<UL><LI>Max Depth = 424<LI>Call Chain = qnb2att &rArr; __hardfp_atan2 &rArr; atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MahonyUpdate
</UL>

<P><STRONG><a name="[156]"></a>rcu_all_reset_flag_clear</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[169]"></a>rcu_clock_freq_get</STRONG> (Thumb, 264 bytes, Stack size 84 bytes, gd32f4xx_rcu.o(i.rcu_clock_freq_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
</UL>

<P><STRONG><a name="[155]"></a>rcu_flag_get</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[118]"></a>rcu_periph_clock_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_clock_enable))
<BR><BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_gpio_init
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[168]"></a>rcu_periph_reset_disable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_disable))
<BR><BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
</UL>

<P><STRONG><a name="[167]"></a>rcu_periph_reset_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_enable))
<BR><BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
</UL>

<P><STRONG><a name="[11b]"></a>rcu_timer_clock_prescaler_config</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_timer_clock_prescaler_config))
<BR><BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[ef]"></a>rtc_flag_clear</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TAMPER_STAMP_IRQHandler
</UL>

<P><STRONG><a name="[ed]"></a>rtc_flag_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TAMPER_STAMP_IRQHandler
</UL>

<P><STRONG><a name="[15d]"></a>rtc_init</STRONG> (Thumb, 190 bytes, Stack size 20 bytes, gd32f4xx_rtc.o(i.rtc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = rtc_init &rArr; rtc_register_sync_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_register_sync_wait
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init_mode_exit
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init_mode_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_setup
</UL>

<P><STRONG><a name="[15e]"></a>rtc_init_mode_enter</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, gd32f4xx_rtc.o(i.rtc_init_mode_enter))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rtc_init_mode_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>

<P><STRONG><a name="[15f]"></a>rtc_init_mode_exit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_init_mode_exit))
<BR><BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>

<P><STRONG><a name="[160]"></a>rtc_register_sync_wait</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, gd32f4xx_rtc.o(i.rtc_register_sync_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rtc_register_sync_wait
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>

<P><STRONG><a name="[161]"></a>rtc_setup</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, bsp_rtc.o(i.rtc_setup))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = rtc_setup &rArr; rtc_init &rArr; rtc_register_sync_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeSync
</UL>

<P><STRONG><a name="[ee]"></a>rtc_show_timestamp</STRONG> (Thumb, 388 bytes, Stack size 32 bytes, bsp_rtc.o(i.rtc_show_timestamp))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = rtc_show_timestamp &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_timestamp_subsecond_get
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_timestamp_get
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TAMPER_STAMP_IRQHandler
</UL>

<P><STRONG><a name="[162]"></a>rtc_timestamp_get</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_timestamp_get))
<BR><BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_show_timestamp
</UL>

<P><STRONG><a name="[163]"></a>rtc_timestamp_subsecond_get</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_timestamp_subsecond_get))
<BR><BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_show_timestamp
</UL>

<P><STRONG><a name="[13b]"></a>rtc_update</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, transplant.o(i.rtc_update))
<BR><BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_navi_and_gnss_send
</UL>

<P><STRONG><a name="[133]"></a>sector_name_to_number</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, bsp_fmc.o(i.sector_name_to_number))
<BR><BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
</UL>

<P><STRONG><a name="[145]"></a>sow2Date</STRONG> (Thumb, 140 bytes, Stack size 104 bytes, time_unify.o(i.sow2Date))
<BR><BR>[Stack]<UL><LI>Max Depth = 392<LI>Call Chain = sow2Date &rArr; gst2time &rArr; epoch2time &rArr; __hardfp_floor &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2epoch
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gst2time
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generateCSVLogFileName
</UL>

<P><STRONG><a name="[e8]"></a>spi_i2s_data_receive</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Exchange
</UL>

<P><STRONG><a name="[e7]"></a>spi_i2s_data_transmit</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Exchange
</UL>

<P><STRONG><a name="[e6]"></a>spi_i2s_flag_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Exchange
</UL>

<P><STRONG><a name="[ff]"></a>sqrt</STRONG> (Thumb, 110 bytes, Stack size 32 bytes, sqrt.o(i.sqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = sqrt &rArr; _dsqrt &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_acos
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[a6]"></a>synthesisLogBuf</STRONG> (Thumb, 644 bytes, Stack size 520 bytes, logger.o(i.synthesisLogBuf))
<BR><BR>[Stack]<UL><LI>Max Depth = 984<LI>Call Chain = synthesisLogBuf &rArr; time2str &rArr; __hardfp_pow &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2str
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2utc
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2time
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseFPGABuff
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI5_9_IRQHandler
</UL>

<P><STRONG><a name="[129]"></a>systick_clksource_set</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, gd32f4xx_misc.o(i.systick_clksource_set))
<BR><BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_init
</UL>

<P><STRONG><a name="[164]"></a>time2epoch</STRONG> (Thumb, 248 bytes, Stack size 56 bytes, time_unify.o(i.time2epoch))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = time2epoch &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2str
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sow2Date
</UL>

<P><STRONG><a name="[165]"></a>time2str</STRONG> (Thumb, 244 bytes, Stack size 160 bytes, time_unify.o(i.time2str))
<BR><BR>[Stack]<UL><LI>Max Depth = 464<LI>Call Chain = time2str &rArr; __hardfp_pow &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2epoch
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
</UL>

<P><STRONG><a name="[f2]"></a>timeSync</STRONG> (Thumb, 290 bytes, Stack size 128 bytes, bsp_rtc.o(i.timeSync))
<BR><BR>[Stack]<UL><LI>Max Depth = 456<LI>Call Chain = timeSync &rArr; gpst2utc &rArr; epoch2time &rArr; __hardfp_floor &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2utc
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2time
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_setup
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;localtime
</UL>
<BR>[Called By]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER0_UP_TIMER9_IRQHandler
</UL>

<P><STRONG><a name="[14d]"></a>timeadd</STRONG> (Thumb, 134 bytes, Stack size 48 bytes, time_unify.o(i.timeadd))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = timeadd &rArr; __hardfp_floor &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floor
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2utc
</UL>

<P><STRONG><a name="[14e]"></a>timediff</STRONG> (Thumb, 78 bytes, Stack size 40 bytes, time_unify.o(i.timediff))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = timediff &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2utc
</UL>

<P><STRONG><a name="[124]"></a>timer_auto_reload_shadow_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_auto_reload_shadow_enable))
<BR><BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[120]"></a>timer_channel_output_config</STRONG> (Thumb, 484 bytes, Stack size 8 bytes, gd32f4xx_timer.o(i.timer_channel_output_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = timer_channel_output_config
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[122]"></a>timer_channel_output_mode_config</STRONG> (Thumb, 90 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_channel_output_mode_config))
<BR><BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[121]"></a>timer_channel_output_pulse_value_config</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config))
<BR><BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[123]"></a>timer_channel_output_shadow_config</STRONG> (Thumb, 90 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_channel_output_shadow_config))
<BR><BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[11c]"></a>timer_deinit</STRONG> (Thumb, 374 bytes, Stack size 8 bytes, gd32f4xx_timer.o(i.timer_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = timer_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[11f]"></a>timer_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_enable))
<BR><BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[11d]"></a>timer_init</STRONG> (Thumb, 122 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_init))
<BR><BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[11e]"></a>timer_interrupt_enable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[f0]"></a>timer_interrupt_flag_get</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER0_UP_TIMER9_IRQHandler
</UL>

<P><STRONG><a name="[de]"></a>uart1sendmsg</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, gd32f4xx_it.o(i.uart1sendmsg))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = uart1sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_enable
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_out
</UL>

<P><STRONG><a name="[cd]"></a>uart3sendmsg422</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, gd32f4xx_it.o(i.uart3sendmsg422))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = uart3sendmsg422 &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_enable
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagCaliDisplay
</UL>

<P><STRONG><a name="[e0]"></a>uart4sendmsg</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, gd32f4xx_it.o(i.uart4sendmsg))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_enable
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_SendMsg
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg_canout
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_out
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
</UL>

<P><STRONG><a name="[159]"></a>uart4sendmsg_canout</STRONG> (Thumb, 118 bytes, Stack size 264 bytes, bsp_can.o(i.uart4sendmsg_canout))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = uart4sendmsg_canout &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[13e]"></a>usart_baudrate_set</STRONG> (Thumb, 224 bytes, Stack size 32 bytes, gd32f4xx_usart.o(i.usart_baudrate_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[f4]"></a>usart_data_receive</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART3_IRQHandler
</UL>

<P><STRONG><a name="[f5]"></a>usart_data_transmit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART3_IRQHandler
</UL>

<P><STRONG><a name="[13d]"></a>usart_deinit</STRONG> (Thumb, 210 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[144]"></a>usart_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_enable))
<BR><BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[134]"></a>usart_flag_get</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[f6]"></a>usart_interrupt_disable</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_interrupt_disable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART3_IRQHandler
</UL>

<P><STRONG><a name="[c1]"></a>usart_interrupt_enable</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_interrupt_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_interrupt_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart3sendmsg422
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1sendmsg
</UL>

<P><STRONG><a name="[f3]"></a>usart_interrupt_flag_get</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, gd32f4xx_usart.o(i.usart_interrupt_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usart_interrupt_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART3_IRQHandler
</UL>

<P><STRONG><a name="[140]"></a>usart_parity_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_parity_config))
<BR><BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[142]"></a>usart_receive_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_receive_config))
<BR><BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[141]"></a>usart_stop_bit_set</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_stop_bit_set))
<BR><BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[143]"></a>usart_transmit_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_transmit_config))
<BR><BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[13f]"></a>usart_word_length_set</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_word_length_set))
<BR><BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[16a]"></a>writeCSVFileHead</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, logger.o(i.writeCSVFileHead))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = writeCSVFileHead &rArr; CH378ByteWrite &rArr; Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteWrite
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
</UL>

<P><STRONG><a name="[a7]"></a>writeCSVLog</STRONG> (Thumb, 330 bytes, Stack size 40 bytes, logger.o(i.writeCSVLog))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = writeCSVLog &rArr; writeCSVFileHead &rArr; CH378ByteWrite &rArr; Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetDiskStatus
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_mDelaymS
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVFileHead
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SetFileName
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378FileOpen
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378FileCreate
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteWrite
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteLocate
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI5_9_IRQHandler
</UL>

<P><STRONG><a name="[9b]"></a>xReadCH378Data</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, ch378_spi_hw.o(i.xReadCH378Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xReadCH378Data &rArr; SPI_Exchange
</UL>
<BR>[Calls]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Exchange
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ReadVar8
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetIntStatus
</UL>

<P><STRONG><a name="[8f]"></a>xWriteCH378Cmd</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, ch378_spi_hw.o(i.xWriteCH378Cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Exchange
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378WriteOfsBlock
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SetFileName
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SendCmdWaitInt
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ReadVar8
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetIntStatus
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteWrite
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteLocate
</UL>

<P><STRONG><a name="[90]"></a>xWriteCH378Data</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, ch378_spi_hw.o(i.xWriteCH378Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xWriteCH378Data &rArr; SPI_Exchange
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Exchange
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378WriteOfsBlock
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SetFileName
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ReadVar8
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteWrite
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteLocate
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[8c]"></a>NVIC_SystemReset</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, bsp_can.o(i.NVIC_SystemReset))
<BR><BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_RX0_IRQHandler
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN0_RX0_IRQHandler
</UL>

<P><STRONG><a name="[166]"></a>system_clock_200m_25m_hxtal</STRONG> (Thumb, 240 bytes, Stack size 0 bytes, system_gd32f4xx.o(i.system_clock_200m_25m_hxtal))
<BR><BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>

<P><STRONG><a name="[ec]"></a>system_clock_config</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.system_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = system_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_200m_25m_hxtal
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[113]"></a>_fp_digits</STRONG> (Thumb, 366 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[f9]"></a>_printf_core</STRONG> (Thumb, 1704 bytes, Stack size 136 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf
</UL>

<P><STRONG><a name="[115]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[114]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[69]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printfa.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0sprintf)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>

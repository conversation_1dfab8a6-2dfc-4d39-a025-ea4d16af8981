/***********************************************************************************
nav Mahony module header
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-5-9          | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/
#ifndef __NAV_MAHONY_H__
#define __NAV_MAHONY_H__
#include "nav_type.h"

enum EAHRSADAPTIVE
{
    E_AHRS_ADAPTIVE_NO =0,       	        //不采取自适应
    E_AHRS_ADAPTIVE_YES=1,		    		//采取自适应
};



void MahonyInit(_NAV_MAHONY_t *pNAV_MAHONY);
void MahonyUpdate(_NAV_Data_Full_t* NAV_Data_Full_p);


#endif

